---
- name: Create directory for certificates
  ansible.builtin.file:
    path: /etc/certs
    state: directory
    mode: '0755'
  register: cert_dir_result
  failed_when: cert_dir_result is failed

- name: Copy certificate file
  ansible.builtin.copy:
    src: cert.pem
    dest: /etc/certs/cert.pem
    mode: '0644'
  register: cert_copy_result
  failed_when: cert_copy_result is failed

- name: Copy key file
  ansible.builtin.copy:
    src: key.pem
    dest: /etc/certs/key.pem
    mode: '0644'
  register: key_copy_result
  failed_when: key_copy_result is failed

- name: Run Vector setup script
  ansible.builtin.shell: |
    bash -c "$(curl -L https://setup.vector.dev)"
  args:
    creates: /etc/apt/sources.list.d/vector.list
  register: vector_setup_result
  failed_when: vector_setup_result.rc != 0

- name: Install Vector using apt
  ansible.builtin.apt:
    name: vector
    state: present
    update_cache: true
  register: apt_install_result
  failed_when: apt_install_result is failed

- name: Add vector user to systemd-journal group
  ansible.builtin.user:
    name: vector
    groups: systemd-journal
    append: true  

- name: Copy Vector configuration file
  ansible.builtin.template:
    src: vector.yaml.j2
    dest: /etc/vector/vector.yaml
    mode: '0644'
  register: config_copy_result
  failed_when: config_copy_result is failed

- name: Ensure journald config has persistent storage and compression
  ansible.builtin.lineinfile:
    path: /etc/systemd/journald.conf
    regexp: '^Storage='
    line: 'Storage=persistent'
    state: present
    create: yes
    backup: yes

- name: Ensure journald config has compression enabled
  ansible.builtin.lineinfile:
    path: /etc/systemd/journald.conf
    regexp: '^Compress='
    line: 'Compress=yes'
    state: present
    create: yes
    backup: yes

- name: Restart systemd-journald to apply changes
  ansible.builtin.systemd:
    name: systemd-journald
    state: restarted  

- name: Enable and start Vector service
  ansible.builtin.systemd:
    name: vector
    enabled: yes 
    state: started
  register: service_result
  failed_when: service_result is failed