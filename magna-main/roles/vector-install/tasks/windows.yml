---
- name: Download Fluent Bit MSI installer
  ansible.windows.win_get_url:
    url: "https://packages.fluentbit.io/windows/fluent-bit-3.2.9-win64.msi"
    dest: "C:\\Users\\<USER>\\Downloads\\fluent-bit.msi"
  register: download_result
  failed_when: download_result is failed

- name: Install Fluent Bit MSI
  ansible.windows.win_package:
    path: "C:\\Users\\<USER>\\Downloads\\fluent-bit.msi"
    state: present
    arguments: /quiet
  register: install_result
  failed_when: install_result is failed

- name: Wait for installation to complete
      win_shell: |
        $timeout = 30  # 
        $timer = 0
        do {
          Start-Sleep -Seconds 5
          $timer += 5
          $service = Get-Service -Name "fluent-bit" -ErrorAction SilentlyContinue
        } while (-not $service -and $timer -lt $timeout)

        if (-not $service) {
          Write-Error "Fluent Bit service not found after installation"
          exit 1
        }
        Write-Output "Fluent Bit service found"
      register: service_wait

- name: Create certificate directory
      win_file:
        path: "{{ cert_path | default('C:\\Program Files\\fluent-bit\\certs') }}"
        state: directory
      register: cert_dir_result

- name: Copy certificate file to C:\certs
  ansible.windows.win_copy:
    src: cert.pem
    dest: "C:\\Program Files\\fluent-bit\\certs\\cert.pem"
  register: cert_copy_result
  failed_when: cert_copy_result is failed

- name: Copy key file to C:\certs
  ansible.windows.win_copy:
    src: key.pem
    dest: "C:\\Program Files\\fluent-bit\\certs\\key.pem"
  register: key_copy_result
  failed_when: key_copy_result is failed

- name: Generate Fluent Bit configuration from template
      win_template:
        src: templates/fluent-bit.conf.j2
        dest: "C:\\Program Files\\fluent-bit\\conf\\fluent-bit.conf"
        backup: yes
      register: config_result

- name: Validate Fluent Bit configuration syntax
      win_shell: |
        Set-Location "C:\Program Files\fluent-bit\bin"
        $output = & .\fluent-bit.exe -c "..\conf\fluent-bit.conf" --dry-run 2>&1
        if ($LASTEXITCODE -ne 0) {
          Write-Error "Configuration validation failed: $output"
          exit 1
        } else {
          Write-Output "Configuration validation passed"
          Write-Output $output
        }
      register: config_validation

- name: Display configuration validation result
      debug:
        msg: "Configuration validation: {{ 'PASSED' if config_validation.rc == 0 else 'FAILED' }}"

- name: Restart Fluent Bit service
  ansible.windows.win_service:
    name: fluent-bit
    state: restarted
  register: service_result
  failed_when: service_result is failed 

- name: Wait for Fluent Bit service to be running
      win_shell: |
        if ($service.Status -ne "Running") {
          Write-Error "Service failed to start within timeout period"
          exit 1
        }
        Write-Output "Fluent Bit service is running"
      register: service_status

- name: Check if Fluent Bit is generating log entries
      win_stat:
        path: "{{ log_path | default('C:\\Program Files\\fluent-bit\\fluent-bit-logs') }}\\fluentbit.log"
      register: log_file_stat

- name: Display service log file status
      debug:
        msg: "Fluent Bit service log file {{ 'exists' if log_file_stat.stat.exists else 'does not exist' }}"

- name: Show recent Fluent Bit log entries (if log file exists)
      win_shell: |
        $logFile = "{{ log_path | default('C:\\Program Files\\fluent-bit\\fluent-bit-logs') }}\\fluentbit.log"
        if (Test-Path $logFile) {
          Write-Output "=== Recent Fluent Bit Log Entries ==="
          Get-Content $logFile -Tail 10 | ForEach-Object { Write-Output $_ }
        } else {
          Write-Output "Log file not found yet - service may still be starting"
        }
      register: recent_logs
      when: log_file_stat.stat.exists

- name: Display recent log entries
      debug:
        msg: "{{ recent_logs.stdout_lines }}"
      when: log_file_stat.stat.exists and recent_logs is defined

- name: Final deployment summary
      debug:
        msg:
          - "=========================================="
          - "Fluent Bit Local Deployment Summary"
          - "=========================================="
          - "Download: {{ 'SUCCESS' if download_result is succeeded else 'FAILED' }}"
          - "Installation: {{ 'SUCCESS' if install_result is succeeded else 'FAILED' }}"
          - "Service Configuration: {{ 'SUCCESS' if service_config is succeeded else 'FAILED' }}"
          - "Certificate Deployment: {{ 'SUCCESS' if (cert_copy_result is succeeded and key_copy_result is succeeded) else 'FAILED' }}"
          - "Configuration Template: {{ 'SUCCESS' if config_result is succeeded else 'FAILED' }}"
          - "Configuration Validation: {{ 'SUCCESS' if config_validation.rc == 0 else 'FAILED' }}"
          - "Service Status: {{ 'RUNNING' if service_start is succeeded else 'FAILED' }}"
          - "Event Log Access: {{ 'SUCCESS' if 'SUCCESS' in event_log_test.stdout else 'LIMITED' }}"
          - "Log File: {{ log_file_stat.stat.path if log_file_stat.stat.exists else 'Not created yet' }}"
          - "=========================================="
          - "Deployment completed on {{ ansible_date_time.date }} at {{ ansible_date_time.time }}"
          - "Host: {{ ansible_hostname }} ({{ ansible_os_name }})"
          - "=========================================="