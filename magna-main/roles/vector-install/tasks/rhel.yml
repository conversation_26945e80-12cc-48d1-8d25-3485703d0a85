---
- name: Create directory for certificates
  ansible.builtin.file:
    path: /etc/certs
    state: directory
    mode: '0755'
  register: cert_dir_result
  failed_when: cert_dir_result is failed
  become: yes

- name: Copy certificate file
  ansible.builtin.copy:
    src: cert.pem
    dest: /etc/certs/cert.pem
    mode: '0644'
  register: cert_copy_result
  failed_when: cert_copy_result is failed
  become: yes

- name: Copy key file
  ansible.builtin.copy:
    src: key.pem
    dest: /etc/certs/key.pem
    mode: '0644'
  register: key_copy_result
  failed_when: key_copy_result is failed
  become: yes

- name: Install Vector using yum
  ansible.builtin.yum:
    name: vector
    state: present
    enablerepo:
      - CPC_Datadog_Vector_for_x86_64_Vector_for_x86_64
  register: yum_install_result
  ignore_errors: yes
  become: yes

- name: Add vector user to systemd-journal group
  ansible.builtin.user:
    name: vector
    groups: systemd-journal
    append: true  
  become: yes

- name: Create directory for Vector configuration
  ansible.builtin.file:
    path: /etc/vector
    state: directory
    mode: '0755'
  register: vector_dir_result
  failed_when: vector_dir_result is failed
  become: yes

- name: Copy Vector configuration file
  ansible.builtin.template:
    src: vector.yaml.j2
    dest: /etc/vector/vector.yaml
    mode: '0644'
  register: config_copy_result
  failed_when: config_copy_result is failed
  become: yes

- name: Enable and start Vector service
  ansible.builtin.systemd:
    name: vector
    enabled: yes 
    state: started
  register: service_result
  failed_when: service_result is failed
  become: yes

- name: Validate Vector configuration
  ansible.builtin.shell:
    cmd: vector validate /etc/vector/vector.yaml
  register: validation_result
  failed_when: "'Validated' not in validation_result.stdout"
  become: yes