# RHEL 8 Rsyslog Configuration and Connectivity Troubleshooting Guide

## Issues Analysis and Root Causes

### 1. Configuration Syntax Errors

#### Issue 1: "Invalid Number" Errors (6 occurrences)
**Root Cause:** The line `$InputTCPServerRun 6514` is conflicting with Vector forwarding on the same port.

**Problem:** Your rsyslog is configured to:
- Listen on TCP port 6514 (`$InputTCPServerRun 6514`)
- Forward to Vector collector on ************:6514

This creates a port conflict and parsing issues.

**Fix:** Change the listening port to 514 (standard syslog port):
```bash
# Change from:
$InputTCPServerRun 6514
# To:
$InputTCPServerRun 514
```

#### Issue 2: Template Error - "Could not find template 0 'mail.none;authpriv.none;cron.none'"
**Root Cause:** Missing or malformed log rule on line 54.

**Problem:** The line is missing the facility/priority part:
```bash
# BROKEN (line 54 in original):
*.info;mail.none;authpriv.none;cron.none;local6.none /var/log/messages
```

**Fix:** Correct the syntax:
```bash
*.info;mail.none;authpriv.none;cron.none                /var/log/messages
```

#### Issue 3: Parsing Error Around Line 54
**Root Cause:** The `$PreserveFQDN on` directive is placed incorrectly within the rules section.

**Fix:** Move global directives before the rules section.

### 2. Network Connectivity Issues

#### Issue: Connection Refused to ************:6514
**Root Causes:**
1. Vector collector may not be running
2. Vector collector may not be listening on the expected interface
3. Firewall blocking connections
4. Network routing issues

## Step-by-Step Remediation Plan

### Phase 1: Fix Configuration Syntax Errors

#### Step 1: Backup Current Configuration
```bash
sudo cp /etc/rsyslog.conf /etc/rsyslog.conf.backup.$(date +%Y%m%d_%H%M%S)
```

#### Step 2: Deploy Fixed Configuration
```bash
# Copy the fixed configuration
sudo cp rsyslog.conf.fixed /etc/rsyslog.conf

# Set proper permissions
sudo chown root:root /etc/rsyslog.conf
sudo chmod 644 /etc/rsyslog.conf
```

#### Step 3: Validate Fixed Configuration
```bash
# Test configuration syntax
sudo rsyslogd -N1 -f /etc/rsyslog.conf

# Expected output: No errors, just version info
```

### Phase 2: Network Connectivity Troubleshooting

#### Step 4: Verify Vector Collector Status
```bash
# Check if Vector service is running on the collector host
# Run these commands on the Vector collector host (************):

# Check Vector service status
sudo systemctl status vector

# Check if Vector is listening on port 6514
sudo netstat -tlnp | grep 6514
# OR
sudo ss -tlnp | grep 6514

# Expected output should show:
# tcp    0    0 0.0.0.0:6514    0.0.0.0:*    LISTEN    <pid>/vector
```

#### Step 5: Test Network Connectivity from RHEL 8 System
```bash
# Test basic network connectivity
ping -c 3 ************

# Test TCP port connectivity
nc -zv ************ 6514
# OR
telnet ************ 6514

# Expected output for successful connection:
# Connection to ************ 6514 port [tcp/*] succeeded!
```

#### Step 6: Check Firewall Rules
```bash
# On RHEL 8 system (client):
sudo firewall-cmd --list-all
sudo firewall-cmd --list-ports

# On Vector collector host (************):
# Check if port 6514 is allowed
sudo firewall-cmd --list-all
sudo firewall-cmd --list-ports

# Add rule if needed:
sudo firewall-cmd --add-port=6514/tcp --permanent
sudo firewall-cmd --reload
```

#### Step 7: Verify Vector Configuration
```bash
# On Vector collector host, check Vector configuration
sudo cat /etc/vector/vector.yaml | grep -A 10 -B 5 6514

# Verify the socket source configuration matches:
# sources:
#   syslog_linux:
#     type: socket
#     mode: tcp
#     address: 0.0.0.0:6514
```

### Phase 3: Service Restart and Validation

#### Step 8: Restart Rsyslog Service
```bash
# Restart rsyslog with fixed configuration
sudo systemctl restart rsyslog

# Check service status
sudo systemctl status rsyslog

# Monitor logs for errors
sudo journalctl -u rsyslog --since "1 minute ago" -f
```

#### Step 9: Test Log Forwarding
```bash
# Send test messages
logger "Test message from $(hostname) - $(date)"
logger -p authpriv.warning "Authentication test message"
logger -p kern.error "Kernel test message"

# Check local logs
sudo tail -5 /var/log/messages

# Monitor rsyslog for forwarding status
sudo journalctl -u rsyslog --since "30 seconds ago"
```

#### Step 10: Monitor Queue Files
```bash
# Check if queue files are created (indicates forwarding attempts)
sudo ls -la /var/lib/rsyslog/vector_forward_queue*

# Monitor queue file growth
watch -n 5 'ls -lah /var/lib/rsyslog/'
```

## Specific Configuration Changes Made

### Original Issues Fixed:

1. **Port Conflict Resolution:**
   ```bash
   # Changed from:
   $InputTCPServerRun 6514
   # To:
   $InputTCPServerRun 514
   ```

2. **Missing Log Rule Fixed:**
   ```bash
   # Added the missing line:
   *.info;mail.none;authpriv.none;cron.none                /var/log/messages
   ```

3. **Global Directive Placement:**
   ```bash
   # Moved $PreserveFQDN on to proper location in global directives section
   ```

4. **Added Proper Queue Configuration for Vector Forwarding:**
   ```bash
   $ActionQueueFileName vector_forward_queue
   $ActionQueueMaxDiskSpace 1g
   $ActionQueueSaveOnShutdown on
   $ActionQueueType LinkedList
   $ActionResumeRetryCount -1
   ```

## Verification Commands

### Configuration Validation:
```bash
# Syntax check
sudo rsyslogd -N1 -f /etc/rsyslog.conf

# Service status
sudo systemctl status rsyslog

# Recent logs
sudo journalctl -u rsyslog --since "5 minutes ago"
```

### Network Connectivity:
```bash
# Test Vector collector connectivity
nc -zv ************ 6514

# Check listening ports
sudo netstat -tlnp | grep rsyslog

# Monitor network connections
sudo netstat -an | grep 6514
```

### Log Forwarding Test:
```bash
# Send test message
logger "CONNECTIVITY_TEST_$(date +%s)"

# Check if message appears in Vector collector logs
# (Run on Vector collector host)
sudo journalctl -u vector --since "30 seconds ago"
```

## Troubleshooting Vector Collector Issues

If connectivity tests fail, check these on the Vector collector host (************):

### 1. Vector Service Status:
```bash
sudo systemctl status vector
sudo systemctl start vector  # if not running
```

### 2. Vector Configuration Validation:
```bash
sudo vector validate /etc/vector/vector.yaml
```

### 3. Vector Logs:
```bash
sudo journalctl -u vector -f
```

### 4. Port Binding Verification:
```bash
sudo ss -tlnp | grep 6514
# Should show: tcp LISTEN 0.0.0.0:6514
```

## Expected Results After Fixes

1. **Configuration validation should show no errors**
2. **Network connectivity test should succeed**
3. **Rsyslog service should start without suspended actions**
4. **Test messages should be forwarded to Vector collector**
5. **No "Connection refused" errors in rsyslog logs**
