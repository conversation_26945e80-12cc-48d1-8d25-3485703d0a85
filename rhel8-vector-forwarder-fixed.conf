#####################################################################
# RHEL 8 Vector Log Forwarder Configuration - FIXED VERSION
# File: /etc/rsyslog.d/50-rhel8-vector-forwarder.conf
#
# Purpose: Forward all RHEL 8 system logs to centralized Vector collector
# Target: Remote RHEL 8 x86_64 systems
# Vector Collector: TCP socket on port 6514
# Rsyslog Version: 8.2102.0 (RHEL 8 default)
#
# FIXES APPLIED:
# 1. Removed duplicate module loading (modules already loaded in main config)
# 2. Fixed syntax errors in conditional statements
# 3. Simplified forwarding rules to avoid template variable issues
# 4. Used standard rsyslog syntax compatible with RHEL 8
#
# Author: System Administrator
# Version: 1.1 (RHEL 8 Compatible - Fixed)
# Date: $(date +%Y-%m-%d)
#####################################################################

#####################################################################
# CONFIGURATION VARIABLES (RHEL 8 Compatible)
#####################################################################

# IMPORTANT: Replace ************* with your actual Vector collector IP address
# This configuration uses direct IP addresses for better RHEL 8 compatibility

#####################################################################
# MODULE LOADING (RHEL 8 Compatible)
#####################################################################

# NOTE: Most modules are already loaded in /etc/rsyslog.conf
# Avoid duplicate loading to prevent errors
# Only load modules if they are not already loaded

# Check if omfwd module is available (may not exist in RHEL 8 base)
# If missing, install: yum install rsyslog-relp rsyslog-gnutls
# Alternative: Use built-in TCP forwarding without explicit module loading

#####################################################################
# GLOBAL CONFIGURATION (RHEL 8 Compatible)
#####################################################################

# Working directory for queue files and state
$WorkDirectory /var/spool/rsyslog

# Message size limits (conservative for RHEL 8)
$MaxMessageSize 64k

# Timestamp format (RHEL 8 compatible)
$ActionFileDefaultTemplate RSYSLOG_TraditionalFileFormat

# Preserve FQDN and structured data
$PreserveFQDN on
$EscapeControlCharactersOnReceive off

#####################################################################
# RHEL 8 MESSAGE TEMPLATES (Simplified)
#####################################################################

# Standard template for RHEL 8 messages
$template RHEL8VectorFormat,"<%PRI%>1 %TIMESTAMP:::date-rfc3339% %HOSTNAME% %APP-NAME% %PROCID% %MSGID% [rhel8@32473 facility=\"%syslogfacility-text%\" severity=\"%syslogseverity-text%\" source_host=\"%HOSTNAME%\"] %MSG%\n"

# Fallback template for compatibility
$template StandardFormat,"<%PRI%>%TIMESTAMP% %HOSTNAME% %syslogtag%%msg%\n"

#####################################################################
# PRIORITY-BASED FORWARDING (RHEL 8 Legacy Syntax)
#####################################################################

# CRITICAL PRIORITY: Configure high-priority queue
$ActionQueueFileName rhel8_critical_vector
$ActionQueueMaxDiskSpace 1g
$ActionQueueSaveOnShutdown on
$ActionQueueType LinkedList
$ActionResumeRetryCount -1
$ActionQueueSize 10000
$ActionQueueDiscardMark 9000
$ActionQueueHighWaterMark 8000
$ActionQueueLowWaterMark 2000

# Forward critical messages
# IMPORTANT: Replace ************* with your Vector collector IP
*.emerg @@*************:6514;RHEL8VectorFormat
*.alert @@*************:6514;RHEL8VectorFormat
*.crit @@*************:6514;RHEL8VectorFormat
authpriv.* @@*************:6514;RHEL8VectorFormat

# Reset queue for medium priority
$ActionQueueFileName rhel8_medium_vector
$ActionQueueMaxDiskSpace 512m
$ActionQueueSize 5000
$ActionQueueDiscardMark 4500
$ActionQueueHighWaterMark 4000
$ActionQueueLowWaterMark 1000

# Forward medium priority messages
kern.* @@*************:6514;RHEL8VectorFormat
daemon.* @@*************:6514;RHEL8VectorFormat
*.err @@*************:6514;RHEL8VectorFormat
*.warning @@*************:6514;RHEL8VectorFormat

# Reset queue for standard priority
$ActionQueueFileName rhel8_standard_vector
$ActionQueueMaxDiskSpace 256m
$ActionQueueSize 3000
$ActionQueueDiscardMark 2700
$ActionQueueHighWaterMark 2500
$ActionQueueLowWaterMark 500

# Forward all other messages
*.info @@*************:6514;RHEL8VectorFormat

#####################################################################
# LOCAL LOGGING PRESERVATION (RHEL 8 Standard)
#####################################################################

# Reset queue settings for local logging
$ActionQueueFileName
$ActionQueueMaxDiskSpace
$ActionQueueSaveOnShutdown off
$ActionQueueType Direct
$ActionResumeRetryCount
$ActionQueueSize
$ActionQueueDiscardMark
$ActionQueueHighWaterMark
$ActionQueueLowWaterMark

# Standard RHEL 8 local logging (preserve existing functionality)
*.info;mail.none;authpriv.none;cron.none    /var/log/messages
authpriv.*                                  /var/log/secure
mail.*                                      /var/log/maillog
cron.*                                      /var/log/cron
*.emerg                                     :omusrmsg:*
uucp,news.crit                             /var/log/spooler
local7.*                                   /var/log/boot.log

#####################################################################
# ERROR HANDLING AND MONITORING (RHEL 8)
#####################################################################

# Log rsyslog errors
$ErrorMessagesToStderr off
$LogRSyslogStatusMessages on

# Monitor forwarding errors (RHEL 8 compatible)
$ActionExecOnlyOnceEveryInterval 300
& /var/log/rsyslog-vector-errors.log

#####################################################################
# RHEL 8 DEPLOYMENT INSTRUCTIONS
#####################################################################

# 1. INSTALL REQUIRED PACKAGES:
#    sudo yum install -y rsyslog
#    sudo yum install -y nc telnet  # For testing connectivity
#
# 2. DEPLOY CONFIGURATION:
#    sudo cp rhel8-vector-forwarder-fixed.conf /etc/rsyslog.d/50-rhel8-vector-forwarder.conf
#    sudo chown root:root /etc/rsyslog.d/50-rhel8-vector-forwarder.conf
#    sudo chmod 644 /etc/rsyslog.d/50-rhel8-vector-forwarder.conf
#
# 3. UPDATE VECTOR COLLECTOR IP:
#    sudo sed -i 's/192\.168\.1\.100/YOUR_VECTOR_IP/g' /etc/rsyslog.d/50-rhel8-vector-forwarder.conf
#
# 4. VALIDATE CONFIGURATION:
#    sudo rsyslogd -N1 -f /etc/rsyslog.conf
#
# 5. TEST CONNECTIVITY:
#    nc -zv YOUR_VECTOR_IP 6514
#
# 6. RESTART SERVICE:
#    sudo systemctl restart rsyslog
#    sudo systemctl status rsyslog
#
# 7. TEST LOGGING:
#    logger "RHEL8 Vector forwarder test from $(hostname)"
#    logger -p authpriv.warning "RHEL8 authentication test"
#
# 8. MONITOR:
#    sudo tail -f /var/log/rsyslog-vector-errors.log
#    sudo ls -la /var/spool/rsyslog/rhel8_*_vector*

#####################################################################
# TROUBLESHOOTING NOTES
#####################################################################

# If omfwd module errors occur:
# 1. Check available modules: ls -la /usr/lib64/rsyslog/
# 2. Install additional packages: yum install rsyslog-relp rsyslog-gnutls
# 3. Use built-in TCP forwarding (no explicit module loading needed)
#
# If duplicate module errors occur:
# 1. Check main config: grep -i "module\|modload" /etc/rsyslog.conf
# 2. Remove duplicate declarations from this file
#
# If syntax errors occur:
# 1. Validate syntax: rsyslogd -N1 -f /etc/rsyslog.d/50-rhel8-vector-forwarder.conf
# 2. Check for special characters and escape sequences
# 3. Use simple forwarding rules instead of complex templates

#####################################################################
# END OF RHEL 8 CONFIGURATION
#####################################################################
