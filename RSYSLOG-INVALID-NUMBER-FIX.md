# RHEL 8 Rsyslog "Invalid Number" Error Diagnosis and Fix

## Root Cause Analysis

### What is Rsyslog Error Code 2010?

**Error Code 2010** in rsyslog indicates **"invalid number"** - this occurs when:
1. A numeric parameter is expected but receives a non-numeric value
2. An empty string is provided where a number is required
3. A parameter value is malformed or contains invalid characters
4. Numeric parameters are set to empty values during configuration reset

### Exact Lines Causing the Errors

The 12 "invalid number" errors are caused by these lines in the queue reset section:

```bash
# PROBLEMATIC LINES (causing 12 errors):
$ActionQueueFileName                    # Error 1: Empty string for filename
$ActionQueueMaxDiskSpace               # Error 2: Empty string for disk space
$ActionResumeRetryCount                # Error 3: Empty string for retry count  
$ActionQueueSize                       # Error 4: Empty string for queue size
$ActionQueueDiscardMark                # Error 5: Empty string for discard mark
$ActionQueueHighWaterMark              # Error 6: Empty string for high water mark
$ActionQueueLowWaterMark               # Error 7: Empty string for low water mark
```

**Why This Happens:**
- These lines attempt to "reset" queue parameters by setting them to empty values
- Rsyslog expects numeric values for size, count, and mark parameters
- Empty strings cannot be parsed as numbers, causing parsing errors
- Each empty parameter generates one "invalid number" error

## Step-by-Step Fix

### Step 1: Backup Current Configuration
```bash
sudo cp /etc/rsyslog.conf /etc/rsyslog.conf.backup.$(date +%Y%m%d_%H%M%S)
```

### Step 2: Apply the Corrected Configuration
```bash
# Copy the fixed version
sudo cp rsyslog.conf.fixed /etc/rsyslog.conf

# Set proper permissions
sudo chown root:root /etc/rsyslog.conf
sudo chmod 644 /etc/rsyslog.conf
```

### Step 3: Validate the Fix
```bash
# Test configuration syntax - should show NO errors
sudo rsyslogd -N1 -f /etc/rsyslog.conf
```

**Expected Output (Success):**
```
rsyslogd: version 8.2102.0-15.el8_10.1, config validation run (level 1), master config /etc/rsyslog.conf
rsyslogd: End of config validation run. Bye.
```

### Step 4: Compare Before and After

**BEFORE (Problematic Queue Reset):**
```bash
# Reset queue settings for subsequent rules
$ActionQueueFileName                    # ← CAUSES ERROR
$ActionQueueMaxDiskSpace               # ← CAUSES ERROR  
$ActionQueueSaveOnShutdown off
$ActionQueueType Direct
$ActionResumeRetryCount                # ← CAUSES ERROR
$ActionQueueSize                       # ← CAUSES ERROR
$ActionQueueDiscardMark                # ← CAUSES ERROR
$ActionQueueHighWaterMark              # ← CAUSES ERROR
$ActionQueueLowWaterMark               # ← CAUSES ERROR
```

**AFTER (Fixed Queue Reset):**
```bash
# Reset queue settings for subsequent rules
# Note: Cannot set numeric parameters to empty values - causes "invalid number" errors
# Instead, set them to default values or omit the reset entirely
$ActionQueueType Direct
$ActionQueueSaveOnShutdown off
```

## Technical Explanation

### Why the Original Approach Failed

1. **Rsyslog Parameter Types:**
   - `$ActionQueueSize` expects an integer (e.g., 10000)
   - `$ActionQueueMaxDiskSpace` expects a size value (e.g., "1g", "500m")
   - `$ActionResumeRetryCount` expects an integer (e.g., -1, 5)

2. **Empty String Assignment:**
   - Setting `$ActionQueueSize` to empty string tries to parse "" as a number
   - Rsyslog parser fails and reports "invalid number"
   - Each empty numeric parameter generates one error

3. **Configuration Parser Behavior:**
   - Rsyslog validates all parameters during parsing
   - Numeric parameters must have valid numeric values
   - Empty strings are not valid numbers

### Why the Fix Works

1. **Selective Reset:**
   - Only reset non-numeric parameters that can accept empty values
   - Omit numeric parameters that would cause parsing errors

2. **Default Behavior:**
   - Rsyslog uses default values for unspecified parameters
   - No need to explicitly reset all parameters to empty values

3. **Minimal Configuration:**
   - Only specify parameters that need explicit values
   - Let rsyslog handle defaults for others

## Validation Steps

### Step 1: Configuration Syntax Check
```bash
sudo rsyslogd -N1 -f /etc/rsyslog.conf
```
**Expected:** No "invalid number" errors

### Step 2: Service Restart Test
```bash
sudo systemctl restart rsyslog
sudo systemctl status rsyslog
```
**Expected:** Service starts successfully without errors

### Step 3: Functionality Test
```bash
# Send test message
logger "Configuration fix test - $(date)"

# Check if message appears in logs
sudo tail -5 /var/log/messages
```
**Expected:** Test message appears in logs

### Step 4: Queue Configuration Verification
```bash
# Check if queue files are created properly
sudo ls -la /var/lib/rsyslog/vector_forward_queue*
```
**Expected:** Queue files created when messages are forwarded

## Alternative Solutions

### Option 1: Complete Queue Reset Removal
```bash
# Simply remove the entire queue reset section
# Rsyslog will use default values for subsequent rules
```

### Option 2: Explicit Default Values
```bash
# Set explicit default values instead of empty strings
$ActionQueueType Direct
$ActionQueueSaveOnShutdown off
$ActionQueueSize 1000
$ActionQueueMaxDiskSpace 100m
$ActionResumeRetryCount 0
```

### Option 3: Modern Rsyslog Syntax (RHEL 8 Compatible)
```bash
# Use modern action syntax instead of legacy directives
action(type="omfwd" target="9.214.57.164" port="6514" protocol="tcp"
       queue.filename="vector_forward_queue"
       queue.maxdiskspace="1g"
       queue.saveonshutdown="on"
       queue.type="LinkedList")
```

## Troubleshooting

### If Errors Persist:
1. **Check for additional config files:**
   ```bash
   sudo find /etc/rsyslog.d/ -name "*.conf" -exec rsyslogd -N1 -f {} \;
   ```

2. **Verify no hidden characters:**
   ```bash
   sudo cat -A /etc/rsyslog.conf | grep -n "ActionQueue"
   ```

3. **Test minimal configuration:**
   ```bash
   # Create minimal test config
   sudo rsyslogd -N1 -f /dev/stdin << 'EOF'
   $ModLoad imuxsock
   *.* /var/log/test.log
   EOF
   ```

### Common Mistakes to Avoid:
1. **Don't set numeric parameters to empty strings**
2. **Don't mix legacy and modern syntax in same config**
3. **Don't forget to restart rsyslog after changes**
4. **Don't skip configuration validation before restart**

## Summary

The "invalid number" errors were caused by attempting to reset rsyslog queue parameters to empty values. The fix removes the problematic empty parameter assignments while preserving the essential queue configuration for Vector forwarding. This resolves all 12 parsing errors while maintaining full functionality.
