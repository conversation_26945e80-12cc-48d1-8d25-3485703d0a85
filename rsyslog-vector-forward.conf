# Rsyslog Configuration for RHEL Client - Vector Collector Forwarding
# Deploy this file to /etc/rsyslog.d/50-vector-forward.conf
# Restart rsyslog service after deployment: systemctl restart rsyslog

#####################################################################
# MODULES
#####################################################################

# Load required modules for TCP forwarding and queuing
module(load="omfwd")     # Output module for forwarding
module(load="imuxsock")  # Input module for local system logging
module(load="imklog")    # Input module for kernel logging

#####################################################################
# GLOBAL DIRECTIVES
#####################################################################

# Set working directory for spool files
$WorkDirectory /var/spool/rsyslog

# Enable high precision timestamps (RFC3339 format)
$ActionFileDefaultTemplate RSYSLOG_TraditionalFileFormat

# Set maximum message size (default is 8k, increase if needed)
$MaxMessageSize 64k

#####################################################################
# QUEUE CONFIGURATION FOR RELIABILITY
#####################################################################

# Configure action queue for reliable forwarding
# This creates a disk-assisted memory queue for better reliability
$ActionQueueFileName vector_forward_queue  # Queue file name prefix
$ActionQueueMaxDiskSpace 1g                # Max disk space for queue
$ActionQueueSaveOnShutdown on              # Save queue on shutdown
$ActionQueueType LinkedList                # Queue type
$ActionResumeRetryCount -1                 # Infinite retries
$ActionQueueSize 10000                     # In-memory queue size
$ActionQueueDiscardMark 9000               # Start discarding at 90% full
$ActionQueueHighWaterMark 8000             # Disk assistance at 80% full
$ActionQueueLowWaterMark 2000              # Resume normal operation
$ActionQueueTimeoutEnqueue 10              # Timeout for queue operations
$ActionQueueDequeueSlowdown 1000           # Slowdown when queue issues occur

#####################################################################
# VECTOR COLLECTOR FORWARDING RULE
#####################################################################

# IMPORTANT: Replace VECTOR_SERVER_IP with your actual Vector collector IP/hostname
# Example: *.* @@*************:6514
# The @@ prefix indicates TCP protocol (@ would be UDP)

# Forward all messages to Vector collector via TCP
# Syntax: facility.priority @@hostname:port
*.* @@VECTOR_SERVER_IP:6514

# Alternative: Forward only specific facilities (uncomment and modify as needed)
# mail.*     @@VECTOR_SERVER_IP:6514  # Mail system messages
# kern.*     @@VECTOR_SERVER_IP:6514  # Kernel messages
# auth.*     @@VECTOR_SERVER_IP:6514  # Authentication messages
# daemon.*   @@VECTOR_SERVER_IP:6514  # System daemon messages

# Alternative: Forward based on severity (uncomment and modify as needed)
# *.err      @@VECTOR_SERVER_IP:6514  # Error level and above
# *.warn     @@VECTOR_SERVER_IP:6514  # Warning level and above
# *.info     @@VECTOR_SERVER_IP:6514  # Info level and above

#####################################################################
# LOCAL LOGGING PRESERVATION
#####################################################################

# Reset queue settings for local logging (to avoid applying queue config to local files)
$ActionQueueFileName
$ActionQueueMaxDiskSpace
$ActionQueueSaveOnShutdown off
$ActionQueueType Direct
$ActionResumeRetryCount
$ActionQueueSize
$ActionQueueDiscardMark
$ActionQueueHighWaterMark
$ActionQueueLowWaterMark
$ActionQueueTimeoutEnqueue
$ActionQueueDequeueSlowdown

# Continue local logging (these rules come after forwarding)
# Messages will be both forwarded AND logged locally

# System messages
*.info;mail.none;authpriv.none;cron.none    /var/log/messages

# Authentication messages
authpriv.*                                  /var/log/secure

# Mail system messages
mail.*                                      /var/log/maillog

# Cron messages
cron.*                                      /var/log/cron

# Emergency messages to all users
*.emerg                                     :omusrmsg:*

# News and UUCP messages
uucp,news.crit                             /var/log/spooler

# Boot messages
local7.*                                   /var/log/boot.log

#####################################################################
# ERROR HANDLING AND FALLBACK
#####################################################################

# If you want to log forwarding errors locally (optional)
# Uncomment the following lines:
# $ActionExecOnlyOnceEveryInterval 300  # Log errors every 5 minutes max
# & /var/log/rsyslog-vector-errors.log

#####################################################################
# STOP PROCESSING RULES (Optional)
#####################################################################

# If you want to ONLY forward to Vector and NOT log locally,
# uncomment the following line after the forwarding rule:
# & stop

# Note: The & stop directive prevents further processing of the message
# Use this only if you want exclusive forwarding without local logging
