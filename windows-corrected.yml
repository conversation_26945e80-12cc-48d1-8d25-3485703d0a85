---
# Corrected Windows Fluent Bit Installation Tasks
# Note: This role should be renamed to 'fluentbit-install' to match its purpose

- name: Verify Windows version compatibility
  ansible.windows.win_shell: |
    $version = [System.Environment]::OSVersion.Version
    if ($version.Major -lt 10) {
      Write-Error "Windows 10 or Server 2016+ required"
      exit 1
    }
    Write-Output "Windows version compatible: $($version.Major).$($version.Minor)"
  register: version_check

- name: Download Fluent Bit MSI installer
  ansible.windows.win_get_url:
    url: "https://packages.fluentbit.io/windows/fluent-bit-3.2.9-win64.msi"
    dest: "C:\\Users\\<USER>\\Downloads\\fluent-bit.msi"
    timeout: 300
  register: download_result

- name: Install Fluent Bit MSI
  ansible.windows.win_package:
    path: "C:\\Users\\<USER>\\Downloads\\fluent-bit.msi"
    state: present
    arguments: /quiet /norestart
  register: install_result

- name: Wait for installation to complete and service to be available
  ansible.windows.win_shell: |
    $timeout = 60
    $timer = 0
    do {
      Start-Sleep -Seconds 5
      $timer += 5
      $service = Get-Service -Name "fluent-bit" -ErrorAction SilentlyContinue
    } while (-not $service -and $timer -lt $timeout)

    if (-not $service) {
      Write-Error "Fluent Bit service not found after installation"
      exit 1
    }
    Write-Output "Fluent Bit service found"
  register: service_wait

- name: Create certificate directory
  ansible.windows.win_file:
    path: "{{ cert_path | default('C:\\Program Files\\fluent-bit\\certs') }}"
    state: directory
  register: cert_dir_result

- name: Copy certificate file to fluent-bit certs directory
  ansible.windows.win_copy:
    src: cert.pem
    dest: "{{ cert_path | default('C:\\Program Files\\fluent-bit\\certs') }}\\cert.pem"
  register: cert_copy_result

- name: Copy key file to fluent-bit certs directory
  ansible.windows.win_copy:
    src: key.pem
    dest: "{{ cert_path | default('C:\\Program Files\\fluent-bit\\certs') }}\\key.pem"
  register: key_copy_result

- name: Generate Fluent Bit configuration from template
  ansible.builtin.template:
    src: templates/fluent-bit.conf.j2
    dest: "C:\\Program Files\\fluent-bit\\conf\\fluent-bit.conf"
    backup: yes
  register: config_result

- name: Validate Fluent Bit configuration syntax
  ansible.windows.win_shell: |
    Set-Location "C:\Program Files\fluent-bit\bin"
    $output = & .\fluent-bit.exe -c "..\conf\fluent-bit.conf" --dry-run 2>&1
    if ($LASTEXITCODE -ne 0) {
      Write-Error "Configuration validation failed: $output"
      exit 1
    } else {
      Write-Output "Configuration validation passed"
      Write-Output $output
    }
  register: config_validation

- name: Display configuration validation result
  ansible.builtin.debug:
    msg: "Configuration validation: {{ 'PASSED' if config_validation.rc == 0 else 'FAILED' }}"

- name: Restart Fluent Bit service
  ansible.windows.win_service:
    name: fluent-bit
    state: restarted
  register: service_result

- name: Wait for Fluent Bit service to be running
  ansible.windows.win_shell: |
    $timeout = 30
    $timer = 0
    do {
      Start-Sleep -Seconds 2
      $timer += 2
      $service = Get-Service -Name "fluent-bit" -ErrorAction SilentlyContinue
    } while ((-not $service -or $service.Status -ne "Running") -and $timer -lt $timeout)
    
    if (-not $service -or $service.Status -ne "Running") {
      Write-Error "Service failed to start within timeout period"
      exit 1
    }
    Write-Output "Fluent Bit service is running"
  register: service_status

- name: Check if Fluent Bit is generating log entries
  ansible.windows.win_stat:
    path: "{{ log_path | default('C:\\Program Files\\fluent-bit\\fluent-bit-logs') }}\\fluentbit.log"
  register: log_file_stat

- name: Display service log file status
  ansible.builtin.debug:
    msg: "Fluent Bit service log file {{ 'exists' if log_file_stat.stat.exists else 'does not exist' }}"

- name: Show recent Fluent Bit log entries (if log file exists)
  ansible.windows.win_shell: |
    $logFile = "{{ log_path | default('C:\\Program Files\\fluent-bit\\fluent-bit-logs') }}\\fluentbit.log"
    if (Test-Path $logFile) {
      Write-Output "=== Recent Fluent Bit Log Entries ==="
      Get-Content $logFile -Tail 10 | ForEach-Object { Write-Output $_ }
    } else {
      Write-Output "Log file not found yet - service may still be starting"
    }
  register: recent_logs
  when: log_file_stat.stat.exists

- name: Display recent log entries
  ansible.builtin.debug:
    msg: "{{ recent_logs.stdout_lines }}"
  when: log_file_stat.stat.exists and recent_logs is defined

- name: Final deployment summary
  ansible.builtin.debug:
    msg:
      - "=========================================="
      - "Fluent Bit Windows Deployment Summary"
      - "=========================================="
      - "Download: {{ 'SUCCESS' if download_result is succeeded else 'FAILED' }}"
      - "Installation: {{ 'SUCCESS' if install_result is succeeded else 'FAILED' }}"
      - "Certificate Directory: {{ 'SUCCESS' if cert_dir_result is succeeded else 'FAILED' }}"
      - "Certificate Deployment: {{ 'SUCCESS' if (cert_copy_result is succeeded and key_copy_result is succeeded) else 'FAILED' }}"
      - "Configuration Template: {{ 'SUCCESS' if config_result is succeeded else 'FAILED' }}"
      - "Configuration Validation: {{ 'SUCCESS' if config_validation.rc == 0 else 'FAILED' }}"
      - "Service Status: {{ 'RUNNING' if service_result is succeeded else 'FAILED' }}"
      - "Log File: {{ log_file_stat.stat.path if log_file_stat.stat.exists else 'Not created yet' }}"
      - "=========================================="
      - "Deployment completed on {{ ansible_date_time.date }} at {{ ansible_date_time.time }}"
      - "Host: {{ ansible_hostname }} ({{ ansible_os_name }})"
      - "=========================================="
