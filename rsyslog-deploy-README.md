# RHEL 9 Vector Log Forwarder Deployment Guide

This README provides comprehensive deployment instructions for the `rhel9-vector-forwarder.conf` rsyslog configuration file, which forwards RHEL 9 system logs to a centralized Vector collector server.

## Table of Contents

- [Overview](#overview)
- [Prerequisites](#prerequisites)
- [System Requirements](#system-requirements)
- [Pre-Deployment Setup](#pre-deployment-setup)
- [Configuration Deployment](#configuration-deployment)
- [Network Configuration](#network-configuration)
- [Validation and Testing](#validation-and-testing)
- [Monitoring and Maintenance](#monitoring-and-maintenance)
- [Troubleshooting](#troubleshooting)
- [Advanced Configuration](#advanced-configuration)
- [Uninstallation](#uninstallation)

## Overview

The `rhel9-vector-forwarder.conf` configuration provides:
- **Reliable TCP forwarding** to Vector collector on port 6514
- **RHEL 9 specific message classification** (systemd, SELinux, audit, authentication)
- **Priority-based queuing** for different message types
- **Structured data preservation** for enhanced log analysis
- **Local logging preservation** for troubleshooting
- **Automatic retry mechanisms** during network outages

## Prerequisites

### Required Packages

Ensure the following packages are installed on your RHEL 9 system:

```bash
# Install required packages
sudo dnf install -y rsyslog rsyslog-mmjsonparse rsyslog-mmnormalize rsyslog-mmfields

# Optional: Install network testing tools
sudo dnf install -y nc telnet bind-utils
```

### Required Services

```bash
# Enable and start rsyslog service
sudo systemctl enable rsyslog
sudo systemctl start rsyslog

# Verify rsyslog is running
sudo systemctl status rsyslog
```

### Verify rsyslog Version

```bash
# Check rsyslog version (should be 8.2102.0 or higher for RHEL 9)
rsyslogd -version
```

## System Requirements

| Component | Requirement |
|-----------|-------------|
| **OS Version** | RHEL 9.x x86_64 |
| **Memory** | Minimum 512MB available for rsyslog queues |
| **Disk Space** | 10GB free space in `/var/spool/rsyslog/` |
| **Network** | Outbound TCP connectivity to Vector collector port 6514 |
| **Privileges** | Root or sudo access required |

## Pre-Deployment Setup

### 1. Create Configuration Backup

```bash
# Backup existing rsyslog configuration
sudo cp /etc/rsyslog.conf /etc/rsyslog.conf.backup.$(date +%Y%m%d_%H%M%S)

# Backup existing rsyslog.d directory
sudo tar -czf /root/rsyslog.d.backup.$(date +%Y%m%d_%H%M%S).tar.gz /etc/rsyslog.d/
```

### 2. Verify System Resources

```bash
# Check available disk space
df -h /var/spool/

# Check memory availability
free -h

# Verify rsyslog working directory exists
sudo mkdir -p /var/spool/rsyslog
sudo chown syslog:adm /var/spool/rsyslog
sudo chmod 755 /var/spool/rsyslog
```

### 3. Document Current Configuration

```bash
# Save current rsyslog configuration for reference
sudo rsyslogd -N1 -f /etc/rsyslog.conf > /tmp/rsyslog-current-config.txt 2>&1
```

## Configuration Deployment

### Step 1: Download and Install Configuration

```bash
# Copy the configuration file to rsyslog.d directory
sudo cp rhel9-vector-forwarder.conf /etc/rsyslog.d/50-rhel9-vector-forwarder.conf

# Set proper ownership and permissions
sudo chown root:root /etc/rsyslog.d/50-rhel9-vector-forwarder.conf
sudo chmod 644 /etc/rsyslog.d/50-rhel9-vector-forwarder.conf
```

### Step 2: Configure Vector Collector IP Address

**Method 1: Direct file editing**
```bash
# Replace placeholder with your Vector collector IP
sudo sed -i 's/192\.168\.1\.100/YOUR_VECTOR_COLLECTOR_IP/g' /etc/rsyslog.d/50-rhel9-vector-forwarder.conf

# Example: Replace with actual IP
sudo sed -i 's/192\.168\.1\.100/*********/g' /etc/rsyslog.d/50-rhel9-vector-forwarder.conf
```

**Method 2: Environment variable (recommended for automation)**
```bash
# Set environment variable
export VECTOR_COLLECTOR_IP="*********"

# Update configuration using environment variable
sudo sed -i "s/192\.168\.1\.100/${VECTOR_COLLECTOR_IP}/g" /etc/rsyslog.d/50-rhel9-vector-forwarder.conf
```

**Method 3: Interactive replacement**
```bash
# Prompt for Vector collector IP
read -p "Enter Vector Collector IP address: " VECTOR_IP
sudo sed -i "s/192\.168\.1\.100/${VECTOR_IP}/g" /etc/rsyslog.d/50-rhel9-vector-forwarder.conf
echo "Configuration updated with Vector collector IP: ${VECTOR_IP}"
```

### Step 3: Validate Configuration Syntax

```bash
# Test rsyslog configuration syntax
sudo rsyslogd -N1 -f /etc/rsyslog.conf

# Check exit code (0 = success)
if [ $? -eq 0 ]; then
    echo "✅ Configuration syntax is valid"
else
    echo "❌ Configuration syntax error - check output above"
    exit 1
fi
```

### Step 4: Verify Configuration Content

```bash
# Display the configured Vector collector IP
grep -n "VectorCollectorIP" /etc/rsyslog.d/50-rhel9-vector-forwarder.conf

# Verify template configuration
grep -A 2 -B 2 "RHEL9VectorFormat" /etc/rsyslog.d/50-rhel9-vector-forwarder.conf
```

## Network Configuration

### 1. Test Vector Collector Connectivity

```bash
# Test TCP connectivity to Vector collector
VECTOR_IP=$(grep 'VectorCollectorIP' /etc/rsyslog.d/50-rhel9-vector-forwarder.conf | cut -d'"' -f2)
nc -zv $VECTOR_IP 6514

# Alternative connectivity test
timeout 5 bash -c "</dev/tcp/$VECTOR_IP/6514" && echo "✅ Port 6514 is reachable" || echo "❌ Port 6514 is not reachable"
```

### 2. Configure Firewall (if needed)

```bash
# Check current firewall status
sudo firewall-cmd --state

# Allow outbound traffic to Vector collector (if firewall blocks outbound)
sudo firewall-cmd --permanent --add-rich-rule="rule family='ipv4' destination address='$VECTOR_IP' port protocol='tcp' port='6514' accept"

# Reload firewall rules
sudo firewall-cmd --reload

# Verify rule was added
sudo firewall-cmd --list-rich-rules
```

### 3. Test DNS Resolution (if using hostname)

```bash
# If using hostname instead of IP, test DNS resolution
nslookup your-vector-collector.example.com
dig your-vector-collector.example.com
```

## Validation and Testing

### 1. Restart rsyslog Service

```bash
# Restart rsyslog to apply new configuration
sudo systemctl restart rsyslog

# Verify service started successfully
sudo systemctl status rsyslog

# Check for any startup errors
sudo journalctl -u rsyslog -n 20 --no-pager
```

### 2. Generate Test Messages

```bash
# Generate test messages for different priorities
logger -p local0.info "RHEL9 Vector forwarder test from $(hostname) at $(date)"
logger -p authpriv.warning "Authentication test message from $(hostname)"
logger -p kern.error "Kernel test message from $(hostname)"
logger -p daemon.info "Systemd daemon test message from $(hostname)"
logger -p mail.notice "Mail system test message from $(hostname)"

# Generate structured test message
logger -p local1.info "Test message with structured data [test@32473 host=\"$(hostname)\" timestamp=\"$(date -Iseconds)\"] Test message content"
```

### 3. Verify Queue Creation

```bash
# Check that queue files are created
sudo ls -la /var/spool/rsyslog/

# Expected queue files:
# - rhel9_critical_vector*
# - rhel9_high_vector*
# - rhel9_standard_vector*
```

### 4. Monitor Log Forwarding

```bash
# Monitor rsyslog for forwarding activity
sudo tail -f /var/log/messages | grep rsyslog

# Check for forwarding errors
sudo tail -f /var/log/rsyslog-vector-errors.log

# Monitor queue statistics
sudo tail -f /var/log/rsyslog-stats.log
```

### 5. Verify Local Logging Still Works

```bash
# Verify local logs are still being written
sudo tail -f /var/log/messages
sudo tail -f /var/log/secure
sudo tail -f /var/log/systemd.log
```

## Monitoring and Maintenance

### Daily Monitoring Commands

```bash
#!/bin/bash
# Daily monitoring script

echo "=== RHEL 9 Vector Forwarder Status ==="
echo "Date: $(date)"
echo

# Check rsyslog service status
echo "📊 Rsyslog Service Status:"
sudo systemctl is-active rsyslog
echo

# Check queue file sizes
echo "📁 Queue File Status:"
sudo ls -lah /var/spool/rsyslog/rhel9_*_vector* 2>/dev/null || echo "No queue files found"
echo

# Check for recent errors
echo "⚠️  Recent Errors (last 10):"
sudo tail -10 /var/log/rsyslog-vector-errors.log 2>/dev/null || echo "No error log found"
echo

# Test connectivity
VECTOR_IP=$(grep 'VectorCollectorIP' /etc/rsyslog.d/50-rhel9-vector-forwarder.conf | cut -d'"' -f2)
echo "🌐 Vector Collector Connectivity:"
nc -zv $VECTOR_IP 6514 2>&1
echo

# Check disk space
echo "💾 Disk Space Usage:"
df -h /var/spool/rsyslog/
```

### Weekly Maintenance Tasks

```bash
#!/bin/bash
# Weekly maintenance script

# Rotate large queue files if needed
find /var/spool/rsyslog/ -name "rhel9_*_vector*" -size +1G -exec ls -lah {} \;

# Clean up old error logs
find /var/log/ -name "rsyslog-vector-errors.log.*" -mtime +30 -delete

# Verify configuration is still valid
sudo rsyslogd -N1 -f /etc/rsyslog.conf
```

### Performance Monitoring

```bash
# Monitor queue performance
sudo grep -i queue /var/log/rsyslog-stats.log | tail -20

# Check memory usage
ps aux | grep rsyslog

# Monitor network connections
sudo ss -tuln | grep 6514
sudo netstat -an | grep 6514
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Configuration Syntax Errors

**Problem**: `rsyslogd -N1` reports syntax errors

**Solution**:
```bash
# Check specific line causing error
sudo rsyslogd -N1 -f /etc/rsyslog.conf 2>&1 | grep -i error

# Validate specific configuration file
sudo rsyslogd -N1 -f /etc/rsyslog.d/50-rhel9-vector-forwarder.conf

# Common fixes:
# - Check for missing quotes around IP address
# - Verify template syntax
# - Ensure all modules are loaded
```

#### 2. Vector Collector Connection Issues

**Problem**: Cannot connect to Vector collector

**Diagnosis**:
```bash
# Test network connectivity
VECTOR_IP=$(grep 'VectorCollectorIP' /etc/rsyslog.d/50-rhel9-vector-forwarder.conf | cut -d'"' -f2)
ping -c 3 $VECTOR_IP
nc -zv $VECTOR_IP 6514
telnet $VECTOR_IP 6514
```

**Solutions**:
```bash
# Check firewall rules
sudo firewall-cmd --list-all

# Verify Vector collector is listening
# (Run on Vector collector server)
sudo ss -tuln | grep 6514

# Check SELinux (if enabled)
sudo sealert -a /var/log/audit/audit.log
```

#### 3. Queue Files Growing Too Large

**Problem**: Queue files in `/var/spool/rsyslog/` are consuming too much disk space

**Solution**:
```bash
# Check current queue sizes
sudo du -sh /var/spool/rsyslog/rhel9_*_vector*

# Temporarily stop rsyslog to clear queues
sudo systemctl stop rsyslog

# Remove large queue files (will lose queued messages)
sudo rm -f /var/spool/rsyslog/rhel9_*_vector*

# Restart rsyslog
sudo systemctl start rsyslog

# Adjust queue sizes in configuration if needed
sudo vi /etc/rsyslog.d/50-rhel9-vector-forwarder.conf
# Reduce queue.maxdiskspace values
```

#### 4. Messages Not Being Forwarded

**Problem**: Local logs are generated but not forwarded to Vector

**Diagnosis**:
```bash
# Check if messages are being processed by the ruleset
sudo grep "rhel9_vector_processing" /var/log/messages

# Verify queue activity
sudo ls -la /var/spool/rsyslog/

# Check for suspension messages
sudo grep -i suspend /var/log/messages
```

**Solution**:
```bash
# Generate test message and monitor
logger "Test message $(date)" &
sudo tail -f /var/log/messages | grep rsyslog

# Check Vector collector logs for received messages
# (Run on Vector collector server)
```

#### 5. High CPU Usage

**Problem**: rsyslog consuming excessive CPU

**Solution**:
```bash
# Check rsyslog process
top -p $(pgrep rsyslogd)

# Reduce queue sizes and processing frequency
sudo vi /etc/rsyslog.d/50-rhel9-vector-forwarder.conf
# Adjust queue.size values
# Add rate limiting if needed

# Restart rsyslog
sudo systemctl restart rsyslog
```

### Log Analysis Commands

```bash
# Search for specific error patterns
sudo grep -i "error\|fail\|suspend" /var/log/messages | grep rsyslog

# Check for configuration reload messages
sudo grep "rsyslogd.*restart\|reload" /var/log/messages

# Monitor real-time rsyslog activity
sudo journalctl -u rsyslog -f

# Check systemd journal for rsyslog messages
sudo journalctl -u rsyslog --since "1 hour ago"
```

## Advanced Configuration

### Customizing Message Filtering

To forward only specific message types, modify the ruleset in the configuration file:

```bash
# Edit configuration
sudo vi /etc/rsyslog.d/50-rhel9-vector-forwarder.conf

# Example: Forward only authentication and audit messages
# Add this condition before the forwarding actions:
if ($!rhel9_msg_type == "AUTHENTICATION" or $!rhel9_msg_type == "AUDIT") then {
    # ... forwarding actions ...
} else {
    stop  # Don't forward other message types
}
```

### Adding Custom Message Types

```bash
# Add custom classification in the ruleset
else if ($programname == "your-custom-app") then {
    set $!rhel9_msg_type = "CUSTOM_APPLICATION";
}
```

### Adjusting Queue Sizes

```bash
# For high-volume environments, increase queue sizes
# Edit the queue.size parameters:
# queue.size="100000"           # Increase from default
# queue.maxdiskspace="10g"      # Increase disk space
```

## Uninstallation

### Complete Removal

```bash
# Stop rsyslog service
sudo systemctl stop rsyslog

# Remove configuration file
sudo rm -f /etc/rsyslog.d/50-rhel9-vector-forwarder.conf

# Remove queue files
sudo rm -f /var/spool/rsyslog/rhel9_*_vector*

# Remove log files
sudo rm -f /var/log/rsyslog-vector-errors.log
sudo rm -f /var/log/rsyslog-stats.log

# Restore original configuration (if backup exists)
sudo cp /etc/rsyslog.conf.backup.* /etc/rsyslog.conf

# Restart rsyslog with original configuration
sudo systemctl start rsyslog
sudo systemctl status rsyslog
```

### Verification of Removal

```bash
# Verify configuration is removed
sudo rsyslogd -N1 -f /etc/rsyslog.conf

# Check that no Vector-related queues exist
sudo ls -la /var/spool/rsyslog/

# Verify local logging still works
logger "Test message after removal"
sudo tail -5 /var/log/messages
```

---

## Support and Documentation

- **RHEL 9 Documentation**: [Red Hat Enterprise Linux 9 Documentation](https://access.redhat.com/documentation/en-us/red_hat_enterprise_linux/9)
- **Rsyslog Documentation**: [Rsyslog Official Documentation](https://www.rsyslog.com/doc/)
- **Vector Documentation**: [Vector Log Collection](https://vector.dev/docs/)

For additional support, check the system logs and use the troubleshooting section above.