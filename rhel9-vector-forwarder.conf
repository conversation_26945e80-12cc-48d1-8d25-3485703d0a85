#####################################################################
# RHEL 9 Vector Log Forwarder Configuration
# File: /etc/rsyslog.d/50-rhel9-vector-forwarder.conf
#
# Purpose: Forward all RHEL 9 system logs to centralized Vector collector
# Target: Remote RHEL 9 x86_64 systems
# Vector Collector: TCP socket on port 6514
#
# Deployment Instructions:
# 1. Copy this file to /etc/rsyslog.d/50-rhel9-vector-forwarder.conf
# 2. Update VECTOR_COLLECTOR_IP variable below with your Vector server IP
# 3. Restart rsyslog: systemctl restart rsyslog
# 4. Verify: systemctl status rsyslog && ss -tuln | grep 6514
# 5. Test connectivity: logger "Test message from $(hostname)"
#
# Network Requirements:
# - Outbound TCP connectivity to Vector collector on port 6514
# - Firewall rules: firewall-cmd --add-port=6514/tcp --permanent (if needed)
#
# Author: System Administrator
# Version: 1.0
# Date: $(date +%Y-%m-%d)
#####################################################################

#####################################################################
# CONFIGURATION VARIABLES
#####################################################################

# IMPORTANT: Replace with your actual Vector collector IP address
# Examples: *************, *********, vector-collector.example.com
$template VectorCollectorIP,"*************"

# Alternative: Use environment variable (export VECTOR_IP=*************)
# $template VectorCollectorIP,`echo $VECTOR_IP`

#####################################################################
# REQUIRED MODULES
#####################################################################

# Core forwarding and input modules
module(load="omfwd")        # Output module for TCP/UDP forwarding
module(load="imuxsock")     # Unix socket input (local system messages)
module(load="imklog")       # Kernel log input module
module(load="imjournal"     # systemd journal input module
       StateFile="imjournal.state"
       Ratelimit.Interval="600"
       Ratelimit.Burst="20000"
       IgnorePreviousMessages="on")

# Message processing modules
module(load="mmjsonparse")  # JSON parsing for structured data
module(load="mmnormalize")  # Message normalization
module(load="mmfields")     # Field extraction capabilities

# Statistics and monitoring (optional)
module(load="impstats" 
       interval="300" 
       severity="info" 
       log.syslog="off" 
       log.file="/var/log/rsyslog-stats.log")

#####################################################################
# GLOBAL CONFIGURATION
#####################################################################

# Working directory for queue files and state
$WorkDirectory /var/spool/rsyslog

# Message size limits (increased for structured data)
$MaxMessageSize 128k

# Timestamp format (RFC3339 for Vector compatibility)
$ActionFileDefaultTemplate RSYSLOG_FileFormat

# Preserve structured data and control characters
$EscapeControlCharactersOnReceive off
$PreserveFQDN on

# Global queue defaults
$ActionQueueTimeoutEnqueue 10
$ActionQueueDequeueSlowdown 1000

#####################################################################
# RHEL 9 MESSAGE CLASSIFICATION TEMPLATE
#####################################################################

# Enhanced template for RHEL 9 messages with structured data
template(name="RHEL9VectorFormat" type="string" 
    string="<%PRI%>1 %TIMESTAMP:::date-rfc3339% %HOSTNAME% %APP-NAME% %PROCID% %MSGID% [rhel9@32473 msg_type=\"%$!rhel9_msg_type%\" service_unit=\"%$!rhel9_service_unit%\" message_id=\"%$!rhel9_message_id%\" process_info=\"%$!rhel9_process_info%\" selinux_context=\"%$!rhel9_selinux_context%\" audit_type=\"%$!rhel9_audit_type%\" systemd_fields=\"%$!rhel9_systemd_fields%\" facility=\"%syslogfacility-text%\" severity=\"%syslogseverity-text%\" source_host=\"%HOSTNAME%\"] %MSG%\n")

# Fallback template for messages that don't match RHEL 9 patterns
template(name="StandardVectorFormat" type="string"
    string="<%PRI%>1 %TIMESTAMP:::date-rfc3339% %HOSTNAME% %APP-NAME% %PROCID% %MSGID% [standard@32473 facility=\"%syslogfacility-text%\" severity=\"%syslogseverity-text%\" source_host=\"%HOSTNAME%\"] %MSG%\n")

#####################################################################
# RHEL 9 MESSAGE PROCESSING RULESET
#####################################################################

# Main processing ruleset for all messages
ruleset(name="rhel9_vector_processing") {
    
    # Initialize RHEL 9 specific variables
    set $!rhel9_msg_type = "";
    set $!rhel9_service_unit = "";
    set $!rhel9_message_id = "";
    set $!rhel9_process_info = "";
    set $!rhel9_selinux_context = "";
    set $!rhel9_audit_type = "";
    set $!rhel9_systemd_fields = "";
    
    # Message type classification based on facility, program, and content
    if ($syslogfacility-text == "daemon" and ($programname == "systemd" or $msg contains "systemd")) then {
        set $!rhel9_msg_type = "SYSTEMD_DAEMON";
        # Extract systemd unit name
        set $!rhel9_service_unit = re_extract($msg, "systemd(?:\\[\\d+\\])?: ([^:]+):", 0, 1, "unknown");
    } else if ($syslogfacility-text == "kern" or $programname == "kernel") then {
        set $!rhel9_msg_type = "KERNEL";
    } else if ($syslogfacility-text == "authpriv" or $programname == "sudo" or $programname == "su" or $programname == "sshd") then {
        set $!rhel9_msg_type = "AUTHENTICATION";
    } else if ($programname == "auditd" or $msg contains "type=AVC" or $msg contains "type=SYSCALL" or $msg contains "audit:") then {
        set $!rhel9_msg_type = "AUDIT";
        # Extract audit record type
        set $!rhel9_audit_type = re_extract($msg, "type=([A-Z_]+)", 0, 1, "UNKNOWN");
    } else if ($msg contains "SELinux" or $msg contains "avc:" or $msg contains "scontext=" or $msg contains "tcontext=") then {
        set $!rhel9_msg_type = "SELINUX";
        # Extract SELinux context information
        set $!rhel9_selinux_context = re_extract($msg, "(scontext=[^\\s]+)", 0, 1, "");
        if ($!rhel9_selinux_context == "") then {
            set $!rhel9_selinux_context = re_extract($msg, "(tcontext=[^\\s]+)", 0, 1, "");
        }
    } else if ($programname == "NetworkManager" or $programname == "dhclient" or $programname == "systemd-networkd" or $programname == "systemd-resolved") then {
        set $!rhel9_msg_type = "NETWORK";
    } else if ($programname == "chronyd" or $programname == "ntpd" or $programname == "systemd-timesyncd") then {
        set $!rhel9_msg_type = "TIME_SYNC";
    } else if ($programname == "firewalld" or $msg contains "iptables" or $msg contains "nftables") then {
        set $!rhel9_msg_type = "FIREWALL";
    } else if ($programname == "dnf" or $programname == "yum" or $programname == "rpm") then {
        set $!rhel9_msg_type = "PACKAGE_MANAGEMENT";
    } else if ($syslogfacility-text == "cron" or $programname == "crond" or $programname contains "systemd" and $msg contains "timer") then {
        set $!rhel9_msg_type = "SCHEDULED_TASK";
    } else if ($syslogfacility-text == "mail" or $programname == "postfix" or $programname == "sendmail") then {
        set $!rhel9_msg_type = "MAIL_SYSTEM";
    } else if ($programname == "dbus" or $programname == "dbus-daemon") then {
        set $!rhel9_msg_type = "DBUS_SYSTEM";
    } else {
        set $!rhel9_msg_type = "GENERAL_SYSTEM";
    }
    
    # Extract systemd journal fields if present
    if ($msg contains "_PID=") then {
        set $!rhel9_process_info = re_extract($msg, "(_PID=[0-9]+)", 0, 1, "");
    }
    
    # Extract MESSAGE_ID for systemd messages
    if ($msg contains "MESSAGE_ID=") then {
        set $!rhel9_message_id = re_extract($msg, "MESSAGE_ID=([a-f0-9-]+)", 0, 1, "");
    }
    
    # Extract additional systemd fields
    if ($msg contains "_UNIT=" or $msg contains "_COMM=" or $msg contains "_EXE=") then {
        set $!rhel9_systemd_fields = re_extract($msg, "(_UNIT=[^\\s]+|_COMM=[^\\s]+|_EXE=[^\\s]+|_UID=[0-9]+|_GID=[0-9]+)", 0, 1, "");
    }
    
    #################################################################
    # PRIORITY-BASED FORWARDING TO VECTOR COLLECTOR
    #################################################################
    
    # CRITICAL PRIORITY: Security, audit, authentication messages
    if ($!rhel9_msg_type == "AUTHENTICATION" or $!rhel9_msg_type == "AUDIT" or $!rhel9_msg_type == "SELINUX" or $syslogseverity <= 2) then {
        action(type="omfwd" 
               target=`echo $VectorCollectorIP` 
               port="6514" 
               protocol="tcp"
               template="RHEL9VectorFormat"
               queue.filename="rhel9_critical_vector"
               queue.maxdiskspace="5g"
               queue.saveonshutdown="on"
               queue.type="LinkedList"
               queue.size="50000"
               queue.discardmark="45000"
               queue.highwatermark="40000"
               queue.lowwatermark="10000"
               queue.timeoutEnqueue="30"
               action.resumeRetryCount="-1"
               action.reportSuspension="on"
               action.reportSuspensionContinuation="on")
    }
    
    # HIGH PRIORITY: System services, kernel, firewall messages
    else if ($!rhel9_msg_type == "SYSTEMD_DAEMON" or $!rhel9_msg_type == "KERNEL" or $!rhel9_msg_type == "FIREWALL" or $!rhel9_msg_type == "NETWORK" or $syslogseverity <= 4) then {
        action(type="omfwd" 
               target=`echo $VectorCollectorIP` 
               port="6514" 
               protocol="tcp"
               template="RHEL9VectorFormat"
               queue.filename="rhel9_high_vector"
               queue.maxdiskspace="3g"
               queue.saveonshutdown="on"
               queue.type="LinkedList"
               queue.size="30000"
               queue.discardmark="27000"
               queue.highwatermark="24000"
               queue.lowwatermark="6000"
               queue.timeoutEnqueue="20"
               action.resumeRetryCount="-1")
    }
    
    # STANDARD PRIORITY: All other messages
    else {
        action(type="omfwd" 
               target=`echo $VectorCollectorIP` 
               port="6514" 
               protocol="tcp"
               template="RHEL9VectorFormat"
               queue.filename="rhel9_standard_vector"
               queue.maxdiskspace="2g"
               queue.saveonshutdown="on"
               queue.type="LinkedList"
               queue.size="20000"
               queue.discardmark="18000"
               queue.highwatermark="15000"
               queue.lowwatermark="4000"
               queue.timeoutEnqueue="15"
               action.resumeRetryCount="-1")
    }
    
    # Continue to local logging (don't stop processing)
}

#####################################################################
# INPUT CONFIGURATION
#####################################################################

# Process all local system messages through RHEL 9 ruleset
$DefaultRuleset rhel9_vector_processing

# Journal input (systemd messages)
input(type="imjournal" ruleset="rhel9_vector_processing")

# Unix socket input (traditional syslog)
input(type="imuxsock" ruleset="rhel9_vector_processing")

# Kernel messages
input(type="imklog" ruleset="rhel9_vector_processing")

#####################################################################
# LOCAL LOGGING PRESERVATION
#####################################################################

# Reset queue settings for local file logging
$ActionQueueFileName
$ActionQueueMaxDiskSpace
$ActionQueueSaveOnShutdown off
$ActionQueueType Direct
$ActionResumeRetryCount
$ActionQueueSize
$ActionQueueDiscardMark
$ActionQueueHighWaterMark
$ActionQueueLowWaterMark
$ActionQueueTimeoutEnqueue
$ActionQueueDequeueSlowdown

# Standard RHEL 9 local logging (preserved for local troubleshooting)
*.info;mail.none;authpriv.none;cron.none    /var/log/messages
authpriv.*                                  /var/log/secure
mail.*                                      /var/log/maillog
cron.*                                      /var/log/cron
*.emerg                                     :omusrmsg:*
uucp,news.crit                             /var/log/spooler
local7.*                                   /var/log/boot.log

# Additional RHEL 9 specific logging
# Systemd journal messages (high-level overview)
:programname, isequal, "systemd"           /var/log/systemd.log

# Security-related messages (compliance requirement)
:msg, contains, "type=AVC"                 /var/log/selinux-avc.log
:msg, contains, "SELinux"                  /var/log/selinux.log

#####################################################################
# ERROR HANDLING AND MONITORING
#####################################################################

# Log rsyslog internal messages for troubleshooting
$ErrorMessagesToStderr off
$LogRSyslogStatusMessages on

# Monitor forwarding errors
$ActionExecOnlyOnceEveryInterval 300
:msg, contains, "suspended"               /var/log/rsyslog-vector-errors.log
:msg, contains, "connection refused"      /var/log/rsyslog-vector-errors.log
:msg, contains, "network unreachable"     /var/log/rsyslog-vector-errors.log

#####################################################################
# CONFIGURATION VALIDATION AND TESTING
#####################################################################

# Test message generation (uncomment for testing)
# Generate test message: logger -p local0.info "RHEL9 Vector forwarder test from $(hostname) at $(date)"

# Validation commands:
# 1. Check configuration syntax: rsyslogd -N1 -f /etc/rsyslog.conf
# 2. Check Vector connectivity: nc -zv VECTOR_COLLECTOR_IP 6514
# 3. Monitor queue status: ls -la /var/spool/rsyslog/
# 4. Check forwarding: tail -f /var/log/rsyslog-vector-errors.log
# 5. Verify Vector reception: check Vector collector logs

#####################################################################
# END OF CONFIGURATION
#####################################################################