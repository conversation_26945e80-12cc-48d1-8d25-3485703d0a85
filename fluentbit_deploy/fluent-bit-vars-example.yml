---
# Ansible Variables for Fluent Bit Configuration
# This file contains the variables used by fluent-bit.conf.j2 template

# =============================================================================
# SERVICE CONFIGURATION
# =============================================================================

# Log level: error, warning, info, debug, trace
fluent_bit_log_level: "warning"

# Flush interval in seconds (how often to send data)
fluent_bit_flush_interval: 5

# Daemon mode: On (background) or Off (foreground)
fluent_bit_daemon_mode: "On"

# Number of worker threads for output
fluent_bit_workers: 2

# HTTP server for metrics (disabled for production security)
fluent_bit_http_server: "Off"
fluent_bit_http_listen: "127.0.0.1"
fluent_bit_http_port: "2020"

# Storage metrics
fluent_bit_storage_metrics: "on"

# =============================================================================
# PATH CONFIGURATION
# =============================================================================

# Base paths for certificates and logs
cert_path: "C:\\Program Files\\fluent-bit\\certs"
log_path: "C:\\Program Files\\fluent-bit\\fluent-bit-logs"

# Certificate and key filenames
cert_filename: "cert.pem"
key_filename: "key.pem"

# =============================================================================
# WINDOWS EVENT LOG INPUT CONFIGURATION
# =============================================================================

# Event log channels to monitor (comma-separated)
winlog_channels: "Security"

# Check interval in seconds
winlog_interval: 2

# SQLite database path for state persistence
winlog_db_path: "prod-winlog.sqlite"

# Tag for routing
winlog_tag: "windows.prod.security"

# =============================================================================
# SPLUNK HEC OUTPUT CONFIGURATION
# =============================================================================

# Splunk HEC endpoint details
splunk_hec_host: "hec.log-collection-cluster-5m-f9e028ed166d24c319c172a5bca39c7c-0000.us-east.containers.appdomain.cloud"
splunk_hec_port: 443
splunk_hec_token: "6e3d4eed-db00-436e-977c-d84809b79ed1"

# Match pattern for routing (* = all events)
splunk_match_pattern: "*"

# TLS/SSL configuration
splunk_tls_enabled: "On"
splunk_tls_verify: "On"

# TLS debug level (0=none, 1-4=increasing verbosity)
# Set to 0 for production, 3 for debugging
tls_debug_level: 0

# Compression method for Splunk output
splunk_compression: "gzip"

# =============================================================================
# TESTING AND DEBUGGING CONFIGURATION
# =============================================================================

# Enable local file output for testing (true/false)
enable_file_output: false

# Enable console output for debugging (true/false)
enable_debug_output: false

# File output configuration (when enabled)
file_output_name: "security-events.log"
file_output_format: "json_lines"

# =============================================================================
# WINDOWS SERVER 2022 DATACENTER PRODUCTION DEFAULTS
# =============================================================================

# Environment identification
environment_name: "production"
environment_type: "prod"
deployment_stage: "production"

# Windows Server 2022 specifications
windows_version: "2022"
windows_edition: "Datacenter"
powershell_version: "5.1"
winrm_version: "3.0"

# Production service settings
fluent_bit_service_name: "fluent-bit"
fluent_bit_service_user: "LocalSystem"
fluent_bit_service_startup: "auto"

# Production monitoring thresholds
monitoring_thresholds:
  cpu_usage_max: 15
  memory_usage_max: 512
  disk_io_max: 50
  network_io_max: 100

# Production backup configuration
backup_config:
  enabled: true
  retention_days: 90
  backup_location: "C:\\fluent-bit-backups"

# Production recovery settings
recovery_config:
  auto_restart_on_failure: true
  max_restart_attempts: 3
  restart_delay: 120

# Network settings optimized for production
network_settings:
  dns_timeout: 10
  connection_timeout: 60
  max_concurrent_connections: 5
  retry_attempts: 3
  retry_delay: 30
