# Fluent Bit Local Deployment Script for Windows
# Simplified deployment for Windows Server 2022 localhost testing

param(
    [Parameter(Position=0)]
    [ValidateSet("production", "debug", "test", "validate", "logs", "help")]
    [string]$Mode = "production"
)

# Script configuration
$Script:Inventory = "inventory\hosts.yml"
$Script:Playbook = "fluentbit_ansible.yaml"
$Script:LogFile = "deployment-$(Get-Date -Format 'yyyyMMdd-HHmmss').log"

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Error-Custom {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Warning-Custom {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Header {
    param([string]$Title)
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host $Title -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
}

# Function to check prerequisites
function Test-Prerequisites {
    Write-Status "Checking prerequisites..."

    # Check if ansible is installed
    try {
        $null = Get-Command ansible -ErrorAction Stop
        Write-Status "✓ Ansible is installed"
    } catch {
        Write-Error-Custom "Ansible is not installed or not in PATH. Please install Ansible first."
        Write-Host "Installation instructions:"
        Write-Host "  pip install ansible"
        Write-Host "  pip install pywinrm"
        exit 1
    }

    # Check if inventory file exists
    if (-not (Test-Path $Script:Inventory)) {
        Write-Error-Custom "Inventory file not found: $Script:Inventory"
        exit 1
    } else {
        Write-Status "✓ Inventory file found"
    }

    # Check if playbook exists
    if (-not (Test-Path $Script:Playbook)) {
        Write-Error-Custom "Playbook not found: $Script:Playbook"
        exit 1
    } else {
        Write-Status "✓ Playbook file found"
    }

    # Check if certificates exist
    if (-not (Test-Path "files\cert.pem")) {
        Write-Warning-Custom "Certificate file not found: files\cert.pem"
        Write-Warning-Custom "Make sure to replace placeholder certificates with real ones"
    } else {
        Write-Status "✓ Certificate file found"
    }

    if (-not (Test-Path "files\key.pem")) {
        Write-Warning-Custom "Private key file not found: files\key.pem"
        Write-Warning-Custom "Make sure to replace placeholder private key with real one"
    } else {
        Write-Status "✓ Private key file found"
    }

    # Check if running as Administrator
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    if (-not $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
        Write-Warning-Custom "Not running as Administrator. Some operations may fail."
        Write-Warning-Custom "Consider running PowerShell as Administrator for best results."
    } else {
        Write-Status "✓ Running as Administrator"
    }

    Write-Status "Prerequisites check completed"
    Write-Host ""
}

# Function to test local execution
function Test-LocalExecution {
    Write-Status "Testing local ansible execution..."

    try {
        $result = ansible localhost -i $Script:Inventory -m setup -a "filter=ansible_os_name" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Status "✓ Local execution test passed"
        } else {
            throw "Local execution test failed"
        }
    } catch {
        Write-Error-Custom "✗ Local execution test failed"
        Write-Error-Custom "Please check:"
        Write-Error-Custom "  1. Ansible is properly installed"
        Write-Error-Custom "  2. Python is available in PATH"
        Write-Error-Custom "  3. Running as Administrator"
        exit 1
    }
    Write-Host ""
}

# Function to deploy in production mode
function Deploy-Production {
    Write-Status "Deploying Fluent Bit in PRODUCTION mode..."
    Write-Status "Configuration:"
    Write-Status "  - Log Level: warning"
    Write-Status "  - File Output: disabled"
    Write-Status "  - Debug Output: disabled"
    Write-Status "  - TLS Debug: 0 (disabled)"
    Write-Status "  - Connection: local (no WinRM)"
    Write-Host ""

    $cmd = "ansible-playbook -i `"$Script:Inventory`" `"$Script:Playbook`" " +
           "-e fluent_bit_log_level=warning " +
           "-e enable_file_output=false " +
           "-e enable_debug_output=false " +
           "-e tls_debug_level=0"

    Write-Status "Executing: $cmd"
    Invoke-Expression $cmd | Tee-Object -FilePath $Script:LogFile
}

# Function to deploy in debug mode
function Deploy-Debug {
    Write-Status "Deploying Fluent Bit in DEBUG mode..."
    Write-Status "Configuration:"
    Write-Status "  - Log Level: debug"
    Write-Status "  - File Output: enabled"
    Write-Status "  - Debug Output: enabled"
    Write-Status "  - TLS Debug: 3 (verbose)"
    Write-Status "  - Local files: C:\fluent-bit-logs\"
    Write-Status "  - Connection: local (no WinRM)"
    Write-Host ""

    $cmd = "ansible-playbook -i `"$Script:Inventory`" `"$Script:Playbook`" " +
           "-e fluent_bit_log_level=debug " +
           "-e enable_file_output=true " +
           "-e enable_debug_output=true " +
           "-e tls_debug_level=3 " +
           "-e file_output_name=debug-security-events.log"

    Write-Status "Executing: $cmd"
    Invoke-Expression $cmd | Tee-Object -FilePath $Script:LogFile
}

# Function to validate deployment
function Test-Deployment {
    Write-Status "Validating deployment..."

    # Check service status
    try {
        $serviceStatus = ansible localhost -i $Script:Inventory -m win_shell -a "Get-Service fluent-bit | Select-Object Status" 2>$null
        if ($serviceStatus -match "Running") {
            Write-Status "✓ Fluent Bit service is running"
        } else {
            Write-Warning-Custom "✗ Fluent Bit service may not be running properly"
        }
    } catch {
        Write-Warning-Custom "✗ Could not check service status"
    }

    # Check log file
    Write-Status "Checking log files..."
    try {
        $logExists = ansible localhost -i $Script:Inventory -m win_shell -a "Test-Path 'C:\Program Files\fluent-bit\fluent-bit-logs\fluentbit.log'" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Status "✓ Service log file exists"
        } else {
            Write-Warning-Custom "✗ Service log file not found"
        }
    } catch {
        Write-Warning-Custom "✗ Could not check service log file"
    }

    # Check configuration
    try {
        $configExists = ansible localhost -i $Script:Inventory -m win_shell -a "Test-Path 'C:\Program Files\fluent-bit\conf\fluent-bit.conf'" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Status "✓ Configuration file deployed"
        } else {
            Write-Error-Custom "✗ Configuration file not found"
        }
    } catch {
        Write-Error-Custom "✗ Could not check configuration file"
    }

    Write-Host ""
}

# Function to show logs
function Show-Logs {
    Write-Status "Recent Fluent Bit service logs:"
    Write-Host "----------------------------------------" -ForegroundColor Yellow
    try {
        ansible localhost -i $Script:Inventory -m win_shell -a "Get-Content 'C:\Program Files\fluent-bit\fluent-bit-logs\fluentbit.log' -Tail 20" 2>$null
    } catch {
        Write-Warning-Custom "Could not retrieve service logs"
    }
    Write-Host ""
}

# Function to show usage
function Show-Usage {
    Write-Host "Fluent Bit Local Deployment Script for Windows" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "USAGE:" -ForegroundColor Yellow
    Write-Host "    .\deploy-local.ps1 [MODE]" -ForegroundColor White
    Write-Host ""
    Write-Host "MODES:" -ForegroundColor Yellow
    Write-Host "    production    Deploy in production mode (default)" -ForegroundColor White
    Write-Host "    debug         Deploy in debug mode with local file output" -ForegroundColor White
    Write-Host "    test          Test connectivity only" -ForegroundColor White
    Write-Host "    validate      Validate existing deployment" -ForegroundColor White
    Write-Host "    logs          Show recent Fluent Bit logs" -ForegroundColor White
    Write-Host "    help          Show this help message" -ForegroundColor White
    Write-Host ""
    Write-Host "EXAMPLES:" -ForegroundColor Yellow
    Write-Host "    .\deploy-local.ps1                    # Deploy in production mode" -ForegroundColor Green
    Write-Host "    .\deploy-local.ps1 debug             # Deploy in debug mode" -ForegroundColor Green
    Write-Host "    .\deploy-local.ps1 test              # Test connectivity" -ForegroundColor Green
    Write-Host "    .\deploy-local.ps1 validate          # Validate deployment" -ForegroundColor Green
    Write-Host ""
    Write-Host "REQUIREMENTS:" -ForegroundColor Yellow
    Write-Host "  • Windows Server 2022 Datacenter Edition" -ForegroundColor White
    Write-Host "  • PowerShell 5.1 or later" -ForegroundColor White
    Write-Host "  • Ansible with ansible.windows collection" -ForegroundColor White
    Write-Host "  • Python (for Ansible local execution)" -ForegroundColor White
    Write-Host "  • Administrator privileges" -ForegroundColor White
    Write-Host ""
}

# Main script logic
Write-Header "Fluent Bit Local Deployment Script"

switch ($Mode) {
    "production" {
        Test-Prerequisites
        Test-LocalExecution
        Deploy-Production
        Test-Deployment
        Write-Status "Production deployment completed successfully!"
        Write-Status "Log saved to: $Script:LogFile"
    }
    "debug" {
        Test-Prerequisites
        Test-LocalExecution
        Deploy-Debug
        Test-Deployment
        Show-Logs
        Write-Status "Debug deployment completed successfully!"
        Write-Status "Log saved to: $Script:LogFile"
        Write-Status "Check C:\fluent-bit-logs\ for event output files"
    }
    "test" {
        Test-Prerequisites
        Test-LocalExecution
        Write-Status "Local execution test completed successfully!"
    }
    "validate" {
        Test-Deployment
        Show-Logs
        Write-Status "Validation completed!"
    }
    "logs" {
        Show-Logs
    }
    "help" {
        Show-Usage
    }
    default {
        Write-Error-Custom "Unknown mode: $Mode"
        Show-Usage
        exit 1
    }
}

Write-Host ""
Write-Header "Deployment script finished"
