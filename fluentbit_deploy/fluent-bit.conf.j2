# Fluent Bit Configuration Template for Windows
# Generated by Ansible - DO NOT EDIT MANUALLY
#
# Template variables:
# - fluent_bit_log_level: Log level (error, warning, info, debug, trace)
# - fluent_bit_flush_interval: Flush interval in seconds
# - fluent_bit_daemon_mode: Run as daemon (On/Off)
# - splunk_hec_host: Splunk HEC endpoint hostname
# - splunk_hec_port: Splunk HEC port (default: 443)
# - splunk_hec_token: Splunk HEC authentication token
# - fluent_bit_workers: Number of worker threads
# - cert_path: Path to certificate directory
# - log_path: Path to fluent-bit logs directory
# - enable_file_output: Enable local file output for testing (true/false)
# - enable_debug_output: Enable console output for debugging (true/false)
# - tls_debug_level: TLS debug level (0-4)

[SERVICE]
    # Flush interval - how often to send data
    flush        {{ fluent_bit_flush_interval | default(1) }}

    # Daemon mode - run in background
    daemon       {{ fluent_bit_daemon_mode | default('On') }}

    # Log level configuration
    log_level    {{ fluent_bit_log_level | default('info') }}

    # Fluent Bit service log file
    log_file     {{ log_path | default('C:\\Program Files\\fluent-bit\\fluent-bit-logs') }}\\fluentbit.log

    # Configuration files
    parsers_file parsers.conf
    plugins_file plugins.conf

    # HTTP Server for metrics (disabled by default for security)
    http_server  {{ fluent_bit_http_server | default('Off') }}
    {% if fluent_bit_http_server | default('Off') == 'On' %}
    http_listen  {{ fluent_bit_http_listen | default('127.0.0.1') }}
    http_port    {{ fluent_bit_http_port | default('2020') }}
    {% endif %}

    # Storage configuration
    storage.metrics {{ fluent_bit_storage_metrics | default('on') }}

[INPUT]
    # Windows Event Log input
    Name         winlog

    # Event log channels to monitor
    Channels     {{ winlog_channels | default('Security') }}

    # Check interval
    Interval_Sec {{ winlog_interval | default(1) }}

    # SQLite database for state persistence
    DB           {{ winlog_db_path | default('winlog.sqlite') }}

    # Tag for routing
    Tag          {{ winlog_tag | default('windows.security') }}

{% if enable_file_output | default(false) %}
# LOCAL FILE OUTPUT - FOR TESTING ONLY
[OUTPUT]
    Name                file
    Match               {{ winlog_tag | default('windows.security') }}
    Path                {{ log_path | default('C:\\Program Files\\fluent-bit\\fluent-bit-logs') }}\\
    File                {{ file_output_name | default('security-events.log') }}
    Format              {{ file_output_format | default('json_lines') }}
    Append              true
    Path_Key            false
{% endif %}

{% if enable_debug_output | default(false) %}
# CONSOLE OUTPUT - FOR DEBUGGING
[OUTPUT]
    Name                stdout
    Match               {{ winlog_tag | default('windows.security') }}
    Format              json_lines
{% endif %}

# SPLUNK HEC OUTPUT - PRODUCTION
[OUTPUT]
    name         splunk
    match        {{ splunk_match_pattern | default('*') }}

    # Splunk HEC endpoint configuration
    host         {{ splunk_hec_host }}
    port         {{ splunk_hec_port | default(443) }}
    splunk_token {{ splunk_hec_token }}

    # Performance and reliability settings
    workers      {{ fluent_bit_workers | default(2) }}

    # TLS/SSL configuration
    tls             {{ splunk_tls_enabled | default('On') }}
    tls.verify      {{ splunk_tls_verify | default('On') }}
    tls.crt_file    {{ cert_path | default('C:\\Program Files\\fluent-bit\\certs') }}\\{{ cert_filename | default('cert.pem') }}
    tls.key_file    {{ cert_path | default('C:\\Program Files\\fluent-bit\\certs') }}\\{{ key_filename | default('key.pem') }}

    # TLS debugging (set to 0 for production)
    tls.debug       {{ tls_debug_level | default(0) }}

    # Optional: Splunk-specific settings
    {% if splunk_channel is defined %}
    splunk_channel  {{ splunk_channel }}
    {% endif %}

    {% if splunk_send_raw | default(false) %}
    splunk_send_raw {{ splunk_send_raw }}
    {% endif %}

    # Compression (optional)
    compress        {{ splunk_compression | default('gzip') }}

# Optional additional outputs can be added here
{% if additional_outputs is defined %}
{% for output in additional_outputs %}

[OUTPUT]
    name         {{ output.name }}
    match        {{ output.match | default('*') }}
    {% for key, value in output.config.items() %}
    {{ key }}        {{ value }}
    {% endfor %}
{% endfor %}
{% endif %}

# vim: ft=dosini
