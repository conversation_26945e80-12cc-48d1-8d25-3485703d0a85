# Fluent Bit Windows Deployment

A comprehensive Ansible automation solution for deploying Fluent Bit on Windows systems with Windows Event Log collection and secure Splunk HEC forwarding.

## 🎯 Overview

This deployment package provides a production-ready Ansible automation framework for:

- **Automated Installation**: Silent MSI installation of Fluent Bit on Windows systems
- **Security-First Configuration**: Mutual TLS authentication with certificate management
- **Event Log Collection**: Windows Security event log monitoring with SQLite persistence
- **Splunk Integration**: Secure forwarding to Splunk HEC endpoints
- **Environment Management**: Separate configurations for development, testing, and production
- **Role-Based Deployment**: Different configurations for domain controllers, file servers, and workstations

## 📂 Directory Structure

```
fluentbit_deploy/
├── README.md                           # This file
├── fluentbit_ansible.yaml              # Main Ansible playbook
├── fluent-bit.conf.j2                  # Jinja2 configuration template (root copy)
├── fluent-bit-vars-example.yml         # Example variables file
│
├── inventory/                          # Ansible inventory and group variables
│   ├── hosts.yml                       # Host definitions and groups
│   └── group_vars/
│       ├── windows.yml                 # Base Windows configuration
│       ├── development.yml             # Development environment overrides
│       └── production.yml              # Production environment overrides
│
├── files/                              # Static files for deployment
│   ├── cert.pem                        # Client certificate (placeholder)
│   └── key.pem                         # Private key (placeholder)
│
└── templates/                          # Jinja2 templates
    └── fluent-bit.conf.j2              # Fluent Bit configuration template
```

## 🔧 Prerequisites

### Control Node Requirements

- **Ansible**: Version 2.9 or later
- **Python**: Version 3.6 or later
- **Ansible Collections**:
  ```bash
  ansible-galaxy collection install ansible.windows
  ansible-galaxy collection install community.windows
  ```

### Target Windows Systems

- **Operating System**: Windows Server 2022 Datacenter Edition (local execution)
- **PowerShell**: Version 5.1 or later
- **Python**: Required for Ansible local execution
- **Administrative Access**: Required for service installation
- **Network Access**: Outbound HTTPS (443) to Splunk HEC endpoint

### Certificates

- **Client Certificate**: X.509 certificate in PEM format
- **Private Key**: Corresponding private key in PEM format
- **CA Trust**: Ensure the Splunk HEC server certificate is trusted

## 🚀 Quick Start

### 1. Certificate Setup

Replace the placeholder certificate files with your actual certificates:

```bash
# Copy your actual certificates
cp /path/to/your/client.pem files/cert.pem
cp /path/to/your/private.key files/key.pem

# Set secure permissions (Linux/macOS)
chmod 600 files/*.pem

# Or on Windows, ensure only administrators have access to the files
```

### 2. Configure Inventory

Edit `inventory/hosts.yml` to match your environment:

```yaml
windows:
  hosts:
    localhost:
      ansible_host: localhost
      ansible_user: administrator
      ansible_password: "YourPasswordHere"
```

### 3. Configure Authentication

Update the inventory file with your local administrator credentials:

```yaml
# Edit inventory/hosts.yml
windows:
  hosts:
    localhost:
      ansible_user: administrator
      ansible_password: "YourActualPassword"
```

### 4. Deploy

Execute the deployment:

```bash
# Test local execution first
ansible localhost -i inventory/hosts.yml -m setup

# Deploy to localhost (production mode)
ansible-playbook -i inventory/hosts.yml \
  fluentbit_ansible.yaml
```

## 📋 Deployment Process

The Ansible playbook performs these steps automatically:

1. **Download** Fluent Bit MSI installer from official repository
2. **Install** Fluent Bit silently with administrative privileges
3. **Configure** Windows service to run as LocalSystem with auto-start
4. **Create** certificate and log directories with proper permissions
5. **Deploy** TLS certificates for mutual authentication
7. **Generate** configuration file from Jinja2 template
8. **Validate** configuration syntax using `--dry-run`
9. **Start** Fluent Bit service and verify operation
10. **Test** Windows Event Log access permissions
11. **Cleanup** temporary installation files

**Note**: This deployment uses Ansible's local connection mode, eliminating the need for WinRM configuration.

## ⚙️ Configuration Management

### Environment-Specific Deployments

The deployment supports multiple environments with different configurations:

#### Local Testing (Production Mode):
```bash
# Deploy with production settings to localhost
ansible-playbook -i inventory/hosts.yml \
  fluentbit_ansible.yaml
```

#### With Debug Output (Optional):
```bash
# Deploy with file output enabled for testing
ansible-playbook -i inventory/hosts.yml \
  -e enable_file_output=true \
  -e enable_debug_output=true \
  -e fluent_bit_log_level=debug \
  fluentbit_ansible.yaml
```

### Role-Based Configuration

Different server roles receive appropriate configurations:

- **Domain Controllers**: Monitor Security, System, Application, and Directory Service logs
- **File Servers**: Monitor Security, System, and Application logs
- **Workstations**: Monitor Security logs only

## 🔍 Validation and Testing

### Post-Deployment Validation

The playbook includes built-in validation:

```bash
# Check service status
ansible localhost -i inventory/hosts.yml -m win_service -a "name=fluent-bit"

# Verify event log access
ansible localhost -i inventory/hosts.yml -m win_shell -a "Get-WinEvent -LogName Security -MaxEvents 1"

# Check log files
ansible localhost -i inventory/hosts.yml -m win_shell -a "Get-Content 'C:\Program Files\fluent-bit\fluent-bit-logs\fluentbit.log' -Tail 10"
```

### Local Testing Mode

For testing, enable local file output:

```bash
# Deploy with local file output for testing
ansible-playbook -i inventory/hosts.yml \
  -e enable_file_output=true \
  -e enable_debug_output=true \
  fluentbit_ansible.yaml
```

This creates local log files at `C:\fluent-bit-logs\` for verification.

## 🛠️ Customization

### Modifying Event Log Channels

Edit group variables to change monitored channels:

```yaml
# For domain controllers
winlog_channels: "Security,System,Application,Directory Service"

# For workstations
winlog_channels: "Security"
```

### Adjusting Performance Settings

Modify worker threads and flush intervals:

```yaml
fluent_bit_workers: 4
fluent_bit_flush_interval: 5
```

### Adding Custom Outputs

Define additional outputs in variables:

```yaml
additional_outputs:
  - name: "file"
    match: "windows.security"
    config:
      Path: "C:\\backup-logs\\"
      File: "backup-security.log"
      Format: "json_lines"
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Service Installation Fails
```bash
# Check Windows Installer service
ansible localhost -i inventory/hosts.yml -m win_shell -a "Get-Service msiserver"

# Verify administrative privileges
ansible localhost -i inventory/hosts.yml -m win_shell -a "whoami /groups | Select-String 'Administrators'"
```

#### 2. Event Log Access Denied
```bash
# Check service account
ansible localhost -i inventory/hosts.yml -m win_shell -a "Get-WmiObject -Class Win32_Service -Filter \"Name='fluent-bit'\" | Select StartName"

# Verify LocalSystem is configured
# Service should run as NT AUTHORITY\SYSTEM
```

#### 3. Network Connectivity Issues
```bash
# Test Splunk HEC endpoint
ansible localhost -i inventory/hosts.yml -m win_shell -a "Test-NetConnection -ComputerName 'hec.log-collection-cluster-5m-f9e028ed166d24c319c172a5bca39c7c-0000.us-east.containers.appdomain.cloud' -Port 443"

# Check certificate files
ansible localhost -i inventory/hosts.yml -m win_shell -a "Test-Path 'C:\Program Files\fluent-bit\certs\cert.pem'"
```

### Debug Mode

Enable detailed logging for troubleshooting:

```yaml
# In your variables
fluent_bit_log_level: "debug"
tls_debug_level: 3
enable_debug_output: true
```

### Log Analysis

Monitor Fluent Bit logs for issues:

```bash
# Real-time log monitoring
ansible localhost -i inventory/hosts.yml -m win_shell -a "Get-Content 'C:\Program Files\fluent-bit\fluent-bit-logs\fluentbit.log' -Wait -Tail 20"
```

## 📊 Monitoring and Maintenance

### Health Checks

Regular health monitoring commands:

```bash
# Service status check
ansible localhost -i inventory/hosts.yml -m win_service -a "name=fluent-bit" --one-line

# Configuration validation
ansible localhost -i inventory/hosts.yml -m win_shell -a "cd 'C:\Program Files\fluent-bit\bin'; .\fluent-bit.exe -c '..\conf\fluent-bit.conf' --dry-run"

# Certificate expiration check
ansible localhost -i inventory/hosts.yml -m win_shell -a "Get-Content 'C:\Program Files\fluent-bit\certs\cert.pem' | Select-String 'CERTIFICATE'"
```

### Log Rotation

Implement log rotation for service logs:

```bash
# Check log file sizes
ansible localhost -i inventory/hosts.yml -m win_shell -a "Get-ChildItem 'C:\Program Files\fluent-bit\fluent-bit-logs\*.log' | Select Name, Length, LastWriteTime"
```

## 🔒 Security Best Practices

1. **Certificate Management**:
   - Use certificates from trusted CAs for production
   - Implement regular certificate rotation (quarterly)
   - Store private keys securely with restricted permissions

2. **Access Control**:
   - Service runs as LocalSystem (NT AUTHORITY\SYSTEM)
   - Certificate directories have restricted access
   - Configuration files are read-only for service account
   - No WinRM exposure (local execution only)

3. **Network Security**:
   - Use firewall rules to restrict outbound connections
   - Monitor network traffic to Splunk endpoints
   - Implement network segmentation where possible

4. **Audit and Compliance**:
   - Log all configuration changes
   - Monitor service start/stop events
   - Regular security assessments of deployed systems

## 📝 File Descriptions

| File | Purpose |
|------|---------|
| `fluentbit_ansible.yaml` | Main Ansible playbook with deployment tasks |
| `fluent-bit.conf.j2` | Jinja2 template for Fluent Bit configuration |
| `fluent-bit-vars-example.yml` | Example variables with all configuration options |
| `inventory/hosts.yml` | Ansible inventory with host definitions |
| `inventory/group_vars/windows.yml` | Base Windows configuration variables |
| `inventory/group_vars/development.yml` | Development environment overrides |
| `inventory/group_vars/production.yml` | Production environment overrides |
| `files/cert.pem` | Client certificate for TLS authentication |
| `files/key.pem` | Private key for TLS authentication |
| `templates/fluent-bit.conf.j2` | Template directory copy of configuration |

## 🤝 Contributing

To contribute to this deployment:

1. Test changes in development environment first
2. Update documentation for any configuration changes
3. Validate against both Windows Server and desktop versions
4. Ensure backward compatibility with existing deployments

## 📞 Support

For issues and questions:

1. Check the troubleshooting section above
2. Review Fluent Bit official documentation
3. Validate Ansible and WinRM connectivity
4. Contact your system administration team

## 📜 License

This deployment automation is provided as-is for organizational use. Ensure compliance with:

- Fluent Bit licensing terms
- Organizational security policies
- Certificate usage agreements
- Splunk licensing requirements

---

**Last Updated**: $(date +%Y-%m-%d)
**Version**: 1.0
**Compatibility**: Fluent Bit 3.2.9+, Ansible 2.9+, Windows Server 2016+
