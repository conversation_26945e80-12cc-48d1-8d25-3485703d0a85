#!/bin/bash

# Fluent Bit Local Deployment Script
# Simplified deployment for Windows Server 2022 localhost testing

set -e  # Exit on any error

echo "========================================"
echo "Fluent Bit Local Deployment Script"
echo "========================================"
echo ""

# Script configuration
INVENTORY="inventory/hosts.yml"
PLAYBOOK="fluentbit_ansible.yaml"
LOG_FILE="deployment-$(date +%Y%m%d-%H%M%S).log"

# Function to print colored output
print_status() {
    echo -e "\e[32m[INFO]\e[0m $1"
}

print_error() {
    echo -e "\e[31m[ERROR]\e[0m $1"
}

print_warning() {
    echo -e "\e[33m[WARNING]\e[0m $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."

    # Check if ansible is installed
    if ! command -v ansible &> /dev/null; then
        print_error "Ansible is not installed. Please install Ansible first."
        exit 1
    fi

    # Check if inventory file exists
    if [ ! -f "$INVENTORY" ]; then
        print_error "Inventory file not found: $INVENTORY"
        exit 1
    fi

    # Check if playbook exists
    if [ ! -f "$PLAYBOOK" ]; then
        print_error "Playbook not found: $PLAYBOOK"
        exit 1
    fi

    # Check if certificates exist
    if [ ! -f "files/cert.pem" ]; then
        print_warning "Certificate file not found: files/cert.pem"
        print_warning "Make sure to replace placeholder certificates with real ones"
    fi

    if [ ! -f "files/key.pem" ]; then
        print_warning "Private key file not found: files/key.pem"
        print_warning "Make sure to replace placeholder private key with real one"
    fi

    print_status "Prerequisites check completed"
    echo ""
}

# Function to test local execution
test_local_execution() {
    print_status "Testing local ansible execution..."

    if ansible localhost -i "$INVENTORY" -m setup -a "filter=ansible_os_name" > /dev/null 2>&1; then
        print_status "✓ Local execution test passed"
    else
        print_error "✗ Local execution test failed"
        print_error "Please check:"
        print_error "  1. Ansible is properly installed"
        print_error "  2. Python is available in PATH"
        print_error "  3. Running as Administrator"
        exit 1
    fi
    echo ""
}

# Function to deploy in production mode
deploy_production() {
    print_status "Deploying Fluent Bit in PRODUCTION mode..."
    print_status "Configuration:"
    print_status "  - Log Level: warning"
    print_status "  - File Output: disabled"
    print_status "  - Debug Output: disabled"
    print_status "  - TLS Debug: 0 (disabled)"
    print_status "  - Connection: local (no WinRM)"
    echo ""

    ansible-playbook -i "$INVENTORY" "$PLAYBOOK" \
        -e fluent_bit_log_level=warning \
        -e enable_file_output=false \
        -e enable_debug_output=false \
        -e tls_debug_level=0 \
        | tee "$LOG_FILE"
}

# Function to deploy in debug mode
deploy_debug() {
    print_status "Deploying Fluent Bit in DEBUG mode..."
    print_status "Configuration:"
    print_status "  - Log Level: debug"
    print_status "  - File Output: enabled"
    print_status "  - Debug Output: enabled"
    print_status "  - TLS Debug: 3 (verbose)"
    print_status "  - Local files: C:\\fluent-bit-logs\\"
    print_status "  - Connection: local (no WinRM)"
    echo ""

    ansible-playbook -i "$INVENTORY" "$PLAYBOOK" \
        -e fluent_bit_log_level=debug \
        -e enable_file_output=true \
        -e enable_debug_output=true \
        -e tls_debug_level=3 \
        -e file_output_name=debug-security-events.log \
        | tee "$LOG_FILE"
}

# Function to validate deployment
validate_deployment() {
    print_status "Validating deployment..."

    # Check service status
    if ansible localhost -i "$INVENTORY" -m win_shell -a "Get-Service fluent-bit | Select-Object Status" | grep -q "Running"; then
        print_status "✓ Fluent Bit service is running"
    else
        print_warning "✗ Fluent Bit service may not be running properly"
    fi

    # Check log file
    print_status "Checking log files..."
    ansible localhost -i "$INVENTORY" -m win_shell -a "Test-Path 'C:\\Program Files\\fluent-bit\\fluent-bit-logs\\fluentbit.log'" > /dev/null 2>&1 && \
        print_status "✓ Service log file exists" || \
        print_warning "✗ Service log file not found"

    # Check configuration
    ansible localhost -i "$INVENTORY" -m win_shell -a "Test-Path 'C:\\Program Files\\fluent-bit\\conf\\fluent-bit.conf'" > /dev/null 2>&1 && \
        print_status "✓ Configuration file deployed" || \
        print_error "✗ Configuration file not found"

    echo ""
}

# Function to show logs
show_logs() {
    print_status "Recent Fluent Bit service logs:"
    echo "----------------------------------------"
    ansible localhost -i "$INVENTORY" -m win_shell -a "Get-Content 'C:\\Program Files\\fluent-bit\\fluent-bit-logs\\fluentbit.log' -Tail 20" 2>/dev/null || \
        print_warning "Could not retrieve service logs"
    echo ""
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  production    Deploy in production mode (default)"
    echo "  debug         Deploy in debug mode with local file output"
    echo "  test          Test connectivity only"
    echo "  validate      Validate existing deployment"
    echo "  logs          Show recent Fluent Bit logs"
    echo "  help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Deploy in production mode"
    echo "  $0 debug             # Deploy in debug mode"
    echo "  $0 test              # Test connectivity"
    echo "  $0 validate          # Validate deployment"
    echo ""
}

# Main script logic
case "${1:-production}" in
    "production")
        check_prerequisites
        test_local_execution
        deploy_production
        validate_deployment
        print_status "Production deployment completed successfully!"
        print_status "Log saved to: $LOG_FILE"
        ;;
    "debug")
        check_prerequisites
        test_local_execution
        deploy_debug
        validate_deployment
        show_logs
        print_status "Debug deployment completed successfully!"
        print_status "Log saved to: $LOG_FILE"
        print_status "Check C:\\fluent-bit-logs\\ for event output files"
        ;;
    "test")
        check_prerequisites
        test_local_execution
        print_status "Local execution test completed successfully!"
        ;;
    "validate")
        validate_deployment
        show_logs
        print_status "Validation completed!"
        ;;
    "logs")
        show_logs
        ;;
    "help"|"-h"|"--help")
        show_usage
        ;;
    *)
        print_error "Unknown option: $1"
        show_usage
        exit 1
        ;;
esac

echo ""
echo "========================================"
echo "Deployment script finished"
echo "========================================"
