---
# Ansible Inventory for Local Fluent Bit Deployment
# Configured for local execution without WinRM

all:
  children:
    windows:
      hosts:
        localhost:
          # Use local connection instead of WinRM
          ansible_connection: local
          ansible_become: yes
          ansible_become_method: runas
          ansible_become_user: SYSTEM

          # Host characteristics
          host_role: production_server
          environment_name: production
          os_version: "Server 2022 Datacenter"

      vars:
        # Local execution settings
        ansible_python_interpreter: auto
        ansible_shell_type: powershell

        # Production mode settings
        fluent_bit_log_level: warning
        fluent_bit_flush_interval: 5
        fluent_bit_workers: 2

        # Local file output disabled for production mode
        enable_file_output: false
        enable_debug_output: false

        # TLS settings for production
        tls_debug_level: 0
        splunk_tls_enabled: "On"
        splunk_tls_verify: "On"

        # Production Splunk HEC configuration
        splunk_hec_host: "hec.log-collection-cluster-5m-f9e028ed166d24c319c172a5bca39c7c-0000.us-east.containers.appdomain.cloud"
        splunk_hec_port: 443
        splunk_hec_token: "6e3d4eed-db00-436e-977c-d84809b79ed1"

        # Windows Event Log settings for production
        winlog_channels: "Security"
        winlog_interval: 2
        winlog_tag: "windows.prod.security"
        winlog_db_path: "prod-winlog.sqlite"

        # Certificate configuration
        cert_path: "C:\\Program Files\\fluent-bit\\certs"
        cert_filename: "cert.pem"
        key_filename: "key.pem"

        # Log paths
        log_path: "C:\\Program Files\\fluent-bit\\fluent-bit-logs"

        # Service configuration
        fluent_bit_service_name: "fluent-bit"
        fluent_bit_service_user: "LocalSystem"
        fluent_bit_service_startup: "auto"

  vars:
    # Global settings for local execution
    ansible_python_interpreter: auto
    gather_timeout: 30
    host_key_checking: false
