---
# Windows Group Variables for Fluent Bit Deployment
# These variables apply to all Windows hosts in the inventory

# =============================================================================
# ANSIBLE WINDOWS CONNECTION CONFIGURATION
# =============================================================================

# WinRM Connection Settings
ansible_connection: winrm
ansible_winrm_transport: basic
ansible_winrm_port: 5985
ansible_winrm_server_cert_validation: ignore
ansible_winrm_connection_timeout: 60
ansible_winrm_read_timeout: 70
ansible_winrm_operation_timeout: 60

# PowerShell Execution Policy
ansible_winrm_kinit_mode: managed
ansible_winrm_kerberos_delegation: false

# =============================================================================
# FLUENT BIT SERVICE CONFIGURATION
# =============================================================================

# Service behavior
fluent_bit_log_level: "info"
fluent_bit_flush_interval: 1
fluent_bit_daemon_mode: "On"
fluent_bit_workers: 2

# HTTP Server (disabled by default for security)
fluent_bit_http_server: "Off"
fluent_bit_http_listen: "127.0.0.1"
fluent_bit_http_port: "2020"

# Storage and buffering
fluent_bit_storage_metrics: "on"

# =============================================================================
# WINDOWS-SPECIFIC PATH CONFIGURATION
# =============================================================================

# Certificate paths (using Windows-style paths)
cert_path: "C:\\Program Files\\fluent-bit\\certs"
cert_filename: "cert.pem"
key_filename: "key.pem"

# Log directories
log_path: "C:\\Program Files\\fluent-bit\\fluent-bit-logs"
output_log_path: "C:\\fluent-bit-logs"

# Fluent Bit installation paths
fluent_bit_install_path: "C:\\Program Files\\fluent-bit"
fluent_bit_config_path: "C:\\Program Files\\fluent-bit\\conf"
fluent_bit_bin_path: "C:\\Program Files\\fluent-bit\\bin"

# =============================================================================
# WINDOWS EVENT LOG INPUT CONFIGURATION
# =============================================================================

# Default event log channels to monitor
winlog_channels: "Security"
winlog_interval: 1
winlog_db_path: "winlog.sqlite"
winlog_tag: "windows.security"

# Extended channels for different server types
# These can be overridden in role-specific group vars
extended_winlog_channels:
  domain_controller: "Security,System,Application,Directory Service"
  file_server: "Security,System,Application"
  application_server: "Security,System,Application"
  workstation: "Security"

# =============================================================================
# SPLUNK HEC OUTPUT CONFIGURATION
# =============================================================================

# Default Splunk HEC settings
splunk_hec_host: "hec.log-collection-cluster-5m-f9e028ed166d24c319c172a5bca39c7c-0000.us-east.containers.appdomain.cloud"
splunk_hec_port: 443
splunk_hec_token: "6e3d4eed-db00-436e-977c-d84809b79ed1"

# TLS Configuration
splunk_tls_enabled: "On"
splunk_tls_verify: "On"
tls_debug_level: 0

# Output matching and compression
splunk_match_pattern: "*"

# =============================================================================
# TESTING AND DEBUGGING CONFIGURATION
# =============================================================================

# Default to production settings (no debug output)
enable_file_output: false
enable_debug_output: false

# File output configuration (when enabled)
file_output_name: "security-events.log"
file_output_format: "json_lines"

# =============================================================================
# WINDOWS SERVICE CONFIGURATION
# =============================================================================

# Service account and startup
fluent_bit_service_name: "fluent-bit"
fluent_bit_service_user: "LocalSystem"
fluent_bit_service_startup: "auto"

# Service dependencies
fluent_bit_service_dependencies:
  - "EventLog"
  - "Winmgmt"

# =============================================================================
# SECURITY AND PERMISSIONS
# =============================================================================

# ACL settings for directories and files
default_acl_settings:
  cert_directory:
    user: "SYSTEM"
    rights: "Read,ReadAndExecute"
    inheritance: "None"

  log_directory:
    user: "SYSTEM"
    rights: "FullControl"
    inheritance: "ContainerInherit,ObjectInherit"

  config_files:
    user: "SYSTEM"
    rights: "Read"
    inheritance: "None"

# Windows security settings
required_privileges:
  - "SeServiceLogonRight" # Log on as a service
  - "SeSecurityPrivilege" # Manage auditing and security log
  - "SeAuditPrivilege" # Generate security audits

# =============================================================================
# PERFORMANCE AND RELIABILITY SETTINGS
# =============================================================================

# Retry and timeout settings
service_start_retries: 5
service_start_delay: 10
config_validation_timeout: 30

# =============================================================================
# MONITORING AND ALERTING
# =============================================================================

# =============================================================================
# BACKUP AND RECOVERY
# =============================================================================

# Backup settings
backup_config:
  enabled: true
  retention_days: 30
  backup_location: "C:\\fluent-bit-backups"

# Recovery settings
recovery_config:
  auto_restart_on_failure: true
  max_restart_attempts: 3
  restart_delay: 60

# =============================================================================
# ENVIRONMENT OVERRIDES
# =============================================================================

# These can be overridden by environment-specific group_vars files

# Windows version specific settings
# Windows Server 2022 Datacenter production defaults
windows_version: "2022"
windows_edition: "Datacenter"
powershell_version: "5.1"
winrm_version: "3.0"
dotnet_version: "4.8"

# Network settings
network_settings:
  dns_timeout: 5
  connection_timeout: 30
  max_concurrent_connections: 10
