---
# Development Environment Variables for Fluent Bit
# These variables override the default windows.yml settings for development environments

# =============================================================================
# DEVELOPMENT SERVICE CONFIGURATION
# =============================================================================

# Enhanced logging for development
fluent_bit_log_level: "debug"
fluent_bit_flush_interval: 1
fluent_bit_daemon_mode: "On"

# Enable HTTP server for monitoring and metrics
fluent_bit_http_server: "On"
fluent_bit_http_listen: "127.0.0.1"
fluent_bit_http_port: "2020"

# Increased workers for development testing
fluent_bit_workers: 2

# =============================================================================
# DEVELOPMENT DEBUGGING CONFIGURATION
# =============================================================================

# Enable all debugging and testing outputs
enable_file_output: true
enable_debug_output: true

# TLS debugging enabled for troubleshooting
tls_debug_level: 3

# File output configuration for development
file_output_name: "dev-security-events.log"
file_output_format: "json_lines"

# =============================================================================
# DEVELOPMENT SPLUNK HEC CONFIGURATION
# =============================================================================

# Development Splunk HEC endpoint
splunk_hec_host: "dev-hec.company.local"
splunk_hec_port: 443
splunk_hec_token: "dev-token-6e3d4eed-db00-436e-977c-d84809b79ed1"

# TLS settings for development (more permissive)
splunk_tls_enabled: "On"
splunk_tls_verify: "Off" # Relaxed for development with self-signed certs

# Development-specific Splunk settings
splunk_compression: "gzip"
splunk_match_pattern: "*"

# Optional development channel
splunk_channel: "dev-channel"

# =============================================================================
# DEVELOPMENT EVENT LOG CONFIGURATION
# =============================================================================

# More comprehensive event log monitoring for development
winlog_channels: "Security,System,Application"
winlog_interval: 1
winlog_tag: "windows.dev.security"

# Development database path
winlog_db_path: "dev-winlog.sqlite"

# =============================================================================
# DEVELOPMENT PATHS AND DIRECTORIES
# =============================================================================

# Development-specific paths
cert_path: "C:\\Program Files\\fluent-bit\\certs"
log_path: "C:\\Program Files\\fluent-bit\\dev-logs"
output_log_path: "C:\\fluent-bit-dev-logs"

# Development certificate files (can be self-signed)
cert_filename: "dev-cert.pem"
key_filename: "dev-key.pem"

# =============================================================================
# DEVELOPMENT PERFORMANCE SETTINGS
# =============================================================================

# Faster intervals for development testing
health_check:
  enabled: true
  interval: 60 # 1 minute for faster feedback
  timeout: 15
  retries: 2

# Development buffer settings (smaller for faster processing)
buffer_settings:
  chunk_size: "512K"
  buffer_max_size: "2M"
  queue_full_action: "Drop_Oldest"

# =============================================================================
# DEVELOPMENT TESTING CONFIGURATION
# =============================================================================

# Additional outputs for development testing
additional_outputs:
  - name: "file"
    match: "windows.dev.security"
    config:
      Path: "C:\\fluent-bit-dev-logs\\"
      File: "dev-backup-security.log"
      Format: "json_lines"

# Development metrics collection
metrics:
  enable_input_metrics: true
  enable_output_metrics: true
  enable_filter_metrics: true

# =============================================================================
# DEVELOPMENT SECURITY SETTINGS (RELAXED)
# =============================================================================

# Relaxed security for development
required_privileges:
  - "SeServiceLogonRight"
  - "SeSecurityPrivilege"

# Development ACL settings (more permissive)
default_acl_settings:
  cert_directory:
    user: "SYSTEM"
    rights: "FullControl"
    inheritance: "ContainerInherit,ObjectInherit"

  log_directory:
    user: "SYSTEM,Users" # Allow Users group for easier debugging
    rights: "FullControl"
    inheritance: "ContainerInherit,ObjectInherit"

# =============================================================================
# DEVELOPMENT BACKUP AND RECOVERY
# =============================================================================

# Development backup settings
backup_config:
  enabled: true
  retention_days: 7 # Shorter retention for development
  backup_location: "C:\\fluent-bit-dev-backups"

# Development recovery settings
recovery_config:
  auto_restart_on_failure: true
  max_restart_attempts: 5 # More attempts for debugging
  restart_delay: 30 # Shorter delay for faster iteration

# =============================================================================
# DEVELOPMENT ANSIBLE SETTINGS
# =============================================================================

# Development-specific Ansible settings
ansible_winrm_connection_timeout: 120 # Longer timeout for debugging
ansible_winrm_read_timeout: 180
ansible_winrm_operation_timeout: 120

# Enhanced verbosity for development
gather_timeout: 60
fact_caching: memory
host_key_checking: false

# =============================================================================
# DEVELOPMENT ENVIRONMENT MARKERS
# =============================================================================

# Environment identification
environment_name: "development"
environment_type: "dev"
deployment_stage: "development"

# Development tags for Splunk
splunk_source: "fluent-bit-dev"
splunk_sourcetype: "windows:security:dev"
splunk_index: "dev-windows-security"

# Development validation settings
skip_production_validations: true
allow_self_signed_certs: true
enable_verbose_logging: true
