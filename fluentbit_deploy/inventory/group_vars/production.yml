---
# Production Environment Variables for Fluent Bit
# These variables override the default windows.yml settings for production environments
# PRODUCTION SETTINGS - HIGH SECURITY AND RELIABILITY

# =============================================================================
# PRODUCTION SERVICE CONFIGURATION
# =============================================================================

# Production logging - minimal verbosity for performance
fluent_bit_log_level: "warning"
fluent_bit_flush_interval: 5
fluent_bit_daemon_mode: "On"

# HTTP server disabled for security in production
fluent_bit_http_server: "Off"
fluent_bit_http_listen: "127.0.0.1"
fluent_bit_http_port: "2020"

# Production worker configuration based on server capacity
fluent_bit_workers: 4

# =============================================================================
# PRODUCTION SECURITY CONFIGURATION
# =============================================================================

# Disable all debugging outputs for production security
enable_file_output: false
enable_debug_output: false

# TLS debugging disabled for production
tls_debug_level: 0

# Production certificate validation - STRICT
splunk_tls_enabled: "On"
splunk_tls_verify: "On"

# =============================================================================
# PRODUCTION SPLUNK HEC CONFIGURATION
# =============================================================================

# Production Splunk HEC endpoint
splunk_hec_host: "hec.log-collection-cluster-5m-f9e028ed166d24c319c172a5bca39c7c-0000.us-east.containers.appdomain.cloud"
splunk_hec_port: 443
splunk_hec_token: "6e3d4eed-db00-436e-977c-d84809b79ed1"

# Production TLS settings - MAXIMUM SECURITY
splunk_tls_enabled: "On"
splunk_tls_verify: "On"

# Production compression and routing
splunk_compression: "gzip"
splunk_match_pattern: "*"

# Production Splunk indexing
splunk_source: "fluent-bit-prod"
splunk_sourcetype: "windows:security:production"
splunk_index: "prod-windows-security"

# =============================================================================
# PRODUCTION EVENT LOG CONFIGURATION
# =============================================================================

# Production event log monitoring - Security focused
winlog_channels: "Security"
winlog_interval: 2
winlog_tag: "windows.prod.security"

# Production database path
winlog_db_path: "prod-winlog.sqlite"

# =============================================================================
# PRODUCTION PATHS AND DIRECTORIES
# =============================================================================

# Standard production paths
cert_path: "C:\\Program Files\\fluent-bit\\certs"
log_path: "C:\\Program Files\\fluent-bit\\logs"
output_log_path: "C:\\fluent-bit-logs"

# Production certificate files - MUST be CA-signed
cert_filename: "logsource_leaf.pem"
key_filename: "logsource_key.pem"

# =============================================================================
# PRODUCTION PERFORMANCE AND RELIABILITY
# =============================================================================

# Production health check settings
health_check:
  enabled: true
  interval: 300    # 5 minutes
  timeout: 30
  retries: 3

# Production buffer settings - optimized for reliability
buffer_settings:
  chunk_size: "2M"
  buffer_max_size: "10M"
  queue_full_action: "Block"

# Production retry and timeout settings
service_start_retries: 3
service_start_delay: 15
config_validation_timeout: 60

# =============================================================================
# PRODUCTION SECURITY HARDENING
# =============================================================================

# Required privileges for production security
required_privileges:
  - "SeServiceLogonRight"
  - "SeSecurityPrivilege"
  - "SeAuditPrivilege"

# Production ACL settings - RESTRICTIVE
default_acl_settings:
  cert_directory:
    user: "SYSTEM"
    rights: "Read"
    inheritance: "None"

  log_directory:
    user: "SYSTEM"
    rights: "FullControl"
    inheritance: "ContainerInherit,ObjectInherit"

  config_files:
    user: "SYSTEM"
    rights: "Read"
    inheritance: "None"

# =============================================================================
# PRODUCTION MONITORING AND METRICS
# =============================================================================

# Production metrics - essential only
metrics:
  enable_input_metrics: true
  enable_output_metrics: true
  enable_filter_metrics: false

# Production monitoring thresholds
monitoring_thresholds:
  cpu_usage_max: 15
  memory_usage_max: 512
  disk_io_max: 50
  network_io_max: 100

# =============================================================================
# PRODUCTION BACKUP AND RECOVERY
# =============================================================================

# Production backup settings - comprehensive
backup_config:
  enabled: true
  retention_days: 90
  backup_location: "C:\\fluent-bit-backups"
  compress_backups: true
  verify_backups: true

# Production recovery settings
recovery_config:
  auto_restart_on_failure: true
  max_restart_attempts: 3
  restart_delay: 120
  escalation_enabled: true

# =============================================================================
# PRODUCTION LOG ROTATION
# =============================================================================

# Production log rotation - prevent disk space issues
log_rotation:
  max_size: "50MB"
  max_files: 10
  rotate_on_startup: true
  compress_old_logs: true

# =============================================================================
# PRODUCTION NETWORK AND CONNECTIVITY
# =============================================================================

# Production network settings - optimized for stability
network_settings:
  dns_timeout: 10
  connection_timeout: 60
  max_concurrent_connections: 5
  retry_attempts: 3
  retry_delay: 30

# Production connection pooling
connection_pool:
  max_connections: 10
  keep_alive: true
  keep_alive_timeout: 300

# =============================================================================
# PRODUCTION ANSIBLE SETTINGS
# =============================================================================

# Production Ansible settings - conservative timeouts
ansible_winrm_connection_timeout: 90
ansible_winrm_read_timeout: 120
ansible_winrm_operation_timeout: 90

# Production deployment settings
gather_timeout: 45
fact_caching: jsonfile
host_key_checking: true

# =============================================================================
# PRODUCTION VALIDATION AND COMPLIANCE
# =============================================================================

# Production validation requirements
validation_requirements:
  certificate_expiry_check: true
  tls_version_minimum: "1.2"
  cipher_suite_validation: true
  service_account_validation: true

# Compliance settings
compliance_settings:
  audit_logging: true
  change_tracking: true
  security_scanning: true
  vulnerability_assessment: true

# =============================================================================
# PRODUCTION ENVIRONMENT IDENTIFICATION
# =============================================================================

# Environment markers
environment_name: "production"
environment_type: "prod"
deployment_stage: "production"

# Production deployment flags
is_production: true
skip_development_features: true
enforce_security_policies: true
require_change_approval: true

# =============================================================================
# PRODUCTION ALERTING AND NOTIFICATIONS
# =============================================================================

# Production alerting configuration
alerting:
  enabled: true
  service_failure_alert: true
  configuration_change_alert: true
  certificate_expiry_alert: true
  performance_threshold_alert: true

# Notification settings
notifications:
  email_alerts: true
  sms_critical: true
  slack_integration: true
  pagerduty_integration: true

# =============================================================================
# PRODUCTION MAINTENANCE WINDOWS
# =============================================================================

# Production maintenance schedule
maintenance_windows:
  primary:
    day: "Sunday"
    start_time: "02:00"
    duration_hours: 4
    timezone: "UTC"

  emergency:
    allowed_anytime: false
    require_approval: true
    minimum_notice_hours: 4

# =============================================================================
# PRODUCTION CAPACITY PLANNING
# =============================================================================

# Resource allocation for production
resource_allocation:
  cpu_reserved_percent: 10
  memory_reserved_mb: 256
  disk_reserved_gb: 5
  network_reserved_mbps: 10

# Scaling thresholds
scaling_thresholds:
  scale_up_cpu: 80
  scale_up_memory: 85
  scale_up_disk: 75
  scale_down_cpu: 20
  scale_down_memory: 30

# =============================================================================
# PRODUCTION DISASTER RECOVERY
# =============================================================================

# Disaster recovery settings
disaster_recovery:
  backup_frequency: "daily"
  offsite_backup: true
  cross_region_replication: true
  rto_minutes: 60  # Recovery Time Objective
  rpo_minutes: 15  # Recovery Point Objective

# Business continuity
business_continuity:
  critical_service: true
  availability_requirement: "99.9%"
  maximum_downtime_minutes: 43  # Per month for 99.9%
