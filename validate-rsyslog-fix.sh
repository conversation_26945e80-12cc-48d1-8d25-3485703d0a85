#!/bin/bash
# Rsyslog "Invalid Number" Error Validation Script

echo "=========================================="
echo "Rsyslog Invalid Number Error Fix Validator"
echo "=========================================="
echo "Date: $(date)"
echo ""

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo "✅ $2"
    else
        echo "❌ $2"
    fi
}

# Function to count errors
count_errors() {
    local error_count=$(echo "$1" | grep -c "invalid number")
    echo "$error_count"
}

echo "Step 1: Backing up current configuration..."
sudo cp /etc/rsyslog.conf /etc/rsyslog.conf.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null
print_status $? "Configuration backed up"

echo ""
echo "Step 2: Testing current configuration..."
current_output=$(sudo rsyslogd -N1 -f /etc/rsyslog.conf 2>&1)
current_errors=$(count_errors "$current_output")
echo "Current 'invalid number' errors: $current_errors"

if [ $current_errors -gt 0 ]; then
    echo "❌ Current configuration has $current_errors invalid number errors"
    echo ""
    echo "Full error output:"
    echo "$current_output"
    echo ""
    echo "Likely causes:"
    echo "- Advanced queue parameters not supported in RHEL 8"
    echo "- Lines 101-104: ActionQueueSize, DiscardMark, HighWaterMark, LowWaterMark"
else
    echo "✅ Current configuration has no invalid number errors"
fi

echo ""
echo "Step 3: Applying fixed configuration..."
if [ -f "rsyslog.conf.fixed" ]; then
    sudo cp rsyslog.conf.fixed /etc/rsyslog.conf
    sudo chown root:root /etc/rsyslog.conf
    sudo chmod 644 /etc/rsyslog.conf
    print_status $? "Fixed configuration applied"
else
    echo "❌ rsyslog.conf.fixed file not found"
    exit 1
fi

echo ""
echo "Step 4: Validating fixed configuration..."
fixed_output=$(sudo rsyslogd -N1 -f /etc/rsyslog.conf 2>&1)
fixed_errors=$(count_errors "$fixed_output")
echo "Fixed configuration 'invalid number' errors: $fixed_errors"

if [ $fixed_errors -eq 0 ]; then
    echo "✅ Fixed configuration has no invalid number errors"
    echo ""
    echo "Validation output:"
    echo "$fixed_output"
else
    echo "❌ Fixed configuration still has $fixed_errors invalid number errors"
    echo ""
    echo "Remaining errors:"
    echo "$fixed_output" | grep "invalid number"
fi

echo ""
echo "Step 5: Testing service restart..."
sudo systemctl restart rsyslog
restart_status=$?
print_status $restart_status "Rsyslog service restarted"

if [ $restart_status -eq 0 ]; then
    echo ""
    echo "Service status:"
    sudo systemctl status rsyslog --no-pager -l | head -10
fi

echo ""
echo "Step 6: Testing log functionality..."
test_message="Rsyslog fix validation test - $(date +%s)"
logger "$test_message"
sleep 2

if grep -q "$test_message" /var/log/messages 2>/dev/null; then
    echo "✅ Test message successfully logged"
else
    echo "❌ Test message not found in logs"
fi

echo ""
echo "=========================================="
echo "SUMMARY"
echo "=========================================="
echo "Before fix: $current_errors invalid number errors"
echo "After fix:  $fixed_errors invalid number errors"

if [ $fixed_errors -eq 0 ] && [ $restart_status -eq 0 ]; then
    echo "✅ SUCCESS: All invalid number errors resolved"
    echo "✅ Rsyslog service is running properly"
    echo ""
    echo "Next steps:"
    echo "1. Test Vector collector connectivity: nc -zv 9.214.57.164 6514"
    echo "2. Monitor logs: sudo journalctl -u rsyslog -f"
    echo "3. Send test messages: logger 'Test message'"
else
    echo "❌ ISSUES REMAIN: Please check the errors above"
    echo ""
    echo "Troubleshooting:"
    echo "1. Check configuration syntax: sudo rsyslogd -N1 -f /etc/rsyslog.conf"
    echo "2. Check service status: sudo systemctl status rsyslog"
    echo "3. Check logs: sudo journalctl -u rsyslog --since '5 minutes ago'"
fi

echo ""
echo "Validation completed: $(date)"
echo "=========================================="
