#!/bin/bash
# RHEL 8 Rsyslog Diagnostic Script
# This script diagnoses rsyslog configuration and connectivity issues

echo "=========================================="
echo "RHEL 8 Rsyslog Diagnostic Script"
echo "=========================================="
echo "Date: $(date)"
echo "Hostname: $(hostname)"
echo "=========================================="

# Function to print section headers
print_section() {
    echo ""
    echo "==========================================
    echo "$1"
    echo "=========================================="
}

# Function to run command and show output
run_command() {
    echo "Command: $1"
    echo "----------------------------------------"
    eval "$1"
    echo ""
}

print_section "1. SYSTEM INFORMATION"
run_command "cat /etc/redhat-release"
run_command "rsyslogd -version"
run_command "systemctl is-active rsyslog"

print_section "2. CONFIGURATION SYNTAX CHECK"
echo "Testing current rsyslog configuration..."
rsyslogd -N1 -f /etc/rsyslog.conf 2>&1 | head -20

print_section "3. RSYSLOG SERVICE STATUS"
run_command "systemctl status rsyslog --no-pager -l"

print_section "4. NETWORK CONNECTIVITY TESTS"
echo "Testing connectivity to Vector collector..."
run_command "ping -c 3 ***********4"
echo "Testing TCP port 6514..."
timeout 5 nc -zv ***********4 6514 2>&1 || echo "Connection to ***********4:6514 failed"

print_section "5. PORT USAGE ANALYSIS"
run_command "netstat -tlnp | grep -E '(514|6514)'"
run_command "ss -tlnp | grep -E '(514|6514)'"

print_section "6. FIREWALL STATUS"
run_command "firewall-cmd --list-all"

print_section "7. RSYSLOG CONFIGURATION ANALYSIS"
echo "Checking for problematic lines in /etc/rsyslog.conf..."
echo "Line 54 area:"
sed -n '50,60p' /etc/rsyslog.conf | nl -v50
echo ""
echo "TCP Server configuration:"
grep -n "InputTCPServerRun" /etc/rsyslog.conf
echo ""
echo "Vector forwarding rules:"
grep -n "***********4" /etc/rsyslog.conf

print_section "8. LOG FILE ANALYSIS"
echo "Recent rsyslog errors:"
journalctl -u rsyslog --since "10 minutes ago" --no-pager | tail -10

print_section "9. QUEUE FILES STATUS"
echo "Checking rsyslog queue files..."
ls -la /var/lib/rsyslog/ | grep -E "(queue|vector)"

print_section "10. RECOMMENDATIONS"
echo "Based on the analysis above:"
echo ""
echo "Configuration Issues Found:"
rsyslogd -N1 -f /etc/rsyslog.conf 2>&1 | grep -E "(invalid|error|Could not find)" | head -5

echo ""
echo "Network Issues Found:"
if ! timeout 5 nc -zv ***********4 6514 >/dev/null 2>&1; then
    echo "- Cannot connect to Vector collector at ***********4:6514"
    echo "- Check if Vector service is running on the collector host"
    echo "- Verify firewall rules allow TCP port 6514"
fi

echo ""
echo "Next Steps:"
echo "1. Fix configuration syntax errors using the provided rsyslog.conf.fixed file"
echo "2. Verify Vector collector is running and listening on 0.0.0.0:6514"
echo "3. Check firewall rules on both client and collector hosts"
echo "4. Restart rsyslog service after fixing configuration"

echo ""
echo "=========================================="
echo "Diagnostic completed: $(date)"
echo "=========================================="
