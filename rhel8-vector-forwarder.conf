#####################################################################
# RHEL 8 Vector Log Forwarder Configuration
# File: /etc/rsyslog.d/50-rhel8-vector-forwarder.conf
#
# Purpose: Forward all RHEL 8 system logs to centralized Vector collector
# Target: Remote RHEL 8 x86_64 systems
# Vector Collector: TCP socket on port 6514
# Rsyslog Version: 8.1911.0 (RHEL 8 default)
#
# RHEL 8 Specific Adaptations:
# - Uses legacy configuration syntax for compatibility
# - Handles older systemd journal field formats
# - Supports traditional syslog alongside systemd journal
# - Accounts for limited module availability in RHEL 8
#
# Deployment Instructions:
# 1. Install required packages: yum install rsyslog rsyslog-mmjsonparse
# 2. Copy this file to /etc/rsyslog.d/50-rhel8-vector-forwarder.conf
# 3. Update VECTOR_COLLECTOR_IP variable below with your Vector server IP
# 4. Restart rsyslog: systemctl restart rsyslog
# 5. Verify: systemctl status rsyslog && ss -tuln | grep 6514
# 6. Test connectivity: logger "Test message from $(hostname)"
#
# Network Requirements:
# - Outbound TCP connectivity to Vector collector on port 6514
# - Firewall rules: firewall-cmd --add-port=6514/tcp --permanent
#
# RHEL 8 Package Requirements:
# - rsyslog (base package)
# - rsyslog-mmjsonparse (for JSON parsing - if available)
# - rsyslog-mmnormalize (for message normalization - if available)
#
# Author: System Administrator
# Version: 1.0 (RHEL 8 Compatible)
# Date: $(date +%Y-%m-%d)
#####################################################################

#####################################################################
# CONFIGURATION VARIABLES (RHEL 8 Compatible)
#####################################################################

# IMPORTANT: Replace with your actual Vector collector IP address
# RHEL 8 Note: Using legacy $template syntax for broader compatibility
$template VectorCollectorIP,"*************"

# Alternative for automation: export VECTOR_IP=************* before rsyslog start
# $template VectorCollectorIP,`echo $VECTOR_IP`

#####################################################################
# REQUIRED MODULES (RHEL 8 Compatible)
#####################################################################

# Core modules (RHEL 8 compatible)
# Note: omfwd module may not be available in RHEL 8 base installation
# Alternative: Use built-in TCP forwarding without explicit module loading
# $ModLoad omfwd        # Output module for TCP/UDP forwarding (may not exist)

# Input modules - only load if not already loaded in main config
# Check /etc/rsyslog.conf first to avoid duplicate loading
# $ModLoad imuxsock     # Unix socket input (likely already loaded)
# $ModLoad imklog       # Kernel log input module (likely already loaded)

# systemd journal module (available in RHEL 8)
# Only configure if not already loaded in main config
# $ModLoad imjournal
# $IMJournalStateFile imjournal.state
# $IMJournalRatelimitInterval 600
# $IMJournalRatelimitBurst 20000
# $IMJournalIgnorePreviousMessages on

# Optional modules (install if available)
# $ModLoad mmjsonparse  # JSON parsing - install rsyslog-mmjsonparse
# $ModLoad mmnormalize  # Message normalization - install rsyslog-mmnormalize

# Statistics module (if available)
# $ModLoad impstats
# $PStatInterval 300

#####################################################################
# GLOBAL CONFIGURATION (RHEL 8 Compatible)
#####################################################################

# Working directory for queue files and state
$WorkDirectory /var/spool/rsyslog

# Message size limits (conservative for RHEL 8)
$MaxMessageSize 64k

# Timestamp format (RHEL 8 compatible)
$ActionFileDefaultTemplate RSYSLOG_TraditionalFileFormat

# Preserve FQDN and structured data
$PreserveFQDN on
$EscapeControlCharactersOnReceive off

# Global queue settings (RHEL 8 legacy syntax)
$ActionQueueTimeoutEnqueue 10
$ActionQueueDequeueSlowdown 1000

#####################################################################
# RHEL 8 MESSAGE TEMPLATES
#####################################################################

# Enhanced template for RHEL 8 messages with structured data
# Note: Using legacy template syntax for RHEL 8 compatibility
$template RHEL8VectorFormat,"<%PRI%>1 %TIMESTAMP:::date-rfc3339% %HOSTNAME% %APP-NAME% %PROCID% %MSGID% [rhel8@32473 msg_type=\"%$!rhel8_msg_type%\" service_unit=\"%$!rhel8_service_unit%\" process_info=\"%$!rhel8_process_info%\" selinux_context=\"%$!rhel8_selinux_context%\" audit_type=\"%$!rhel8_audit_type%\" facility=\"%syslogfacility-text%\" severity=\"%syslogseverity-text%\" source_host=\"%HOSTNAME%\"] %MSG%\n"

# Standard template for non-classified messages
$template StandardVectorFormat,"<%PRI%>1 %TIMESTAMP:::date-rfc3339% %HOSTNAME% %APP-NAME% %PROCID% %MSGID% [standard@32473 facility=\"%syslogfacility-text%\" severity=\"%syslogseverity-text%\" source_host=\"%HOSTNAME%\"] %MSG%\n"

#####################################################################
# RHEL 8 MESSAGE CLASSIFICATION (Legacy Syntax)
#####################################################################

# Initialize RHEL 8 specific variables using legacy syntax
$template rhel8_msg_type,""
$template rhel8_service_unit,""
$template rhel8_process_info,""
$template rhel8_selinux_context,""
$template rhel8_audit_type,""

# RHEL 8 Message Classification Rules (using legacy if-then syntax)

# systemd daemon messages
# Fixed syntax: Use proper conditional structure for RHEL 8
if ($syslogfacility-text == "daemon" and $programname == "systemd") then {
    set $!rhel8_msg_type = "SYSTEMD_DAEMON";
    set $!rhel8_service_unit = re_extract($msg, "systemd(?:\\\\[\\\\d+\\\\])?: ([^:]+):", 0, 1, "unknown");
}

# Kernel messages
if ($syslogfacility-text == "kern") then {
    set $!rhel8_msg_type = "KERNEL";
}

# Authentication messages (RHEL 8 specific programs)
if ($syslogfacility-text == "authpriv") then {
    set $!rhel8_msg_type = "AUTHENTICATION";
}
if ($programname == "sudo") then {
    set $!rhel8_msg_type = "AUTHENTICATION";
}
if ($programname == "su") then {
    set $!rhel8_msg_type = "AUTHENTICATION";
}
if ($programname == "sshd") then {
    set $!rhel8_msg_type = "AUTHENTICATION";
}

# Audit messages (RHEL 8 format)
if ($programname == "auditd") then {
    set $!rhel8_msg_type = "AUDIT";
    set $!rhel8_audit_type = re_extract($msg, "type=([A-Z_]+)", 0, 1, "UNKNOWN");
}
if ($msg contains "type=AVC") then {
    set $!rhel8_msg_type = "AUDIT";
    set $!rhel8_audit_type = "AVC";
}
if ($msg contains "type=SYSCALL") then {
    set $!rhel8_msg_type = "AUDIT";
    set $!rhel8_audit_type = "SYSCALL";
}

# SELinux messages (RHEL 8 format)
if ($msg contains "SELinux") then {
    set $!rhel8_msg_type = "SELINUX";
    set $!rhel8_selinux_context = re_extract($msg, "(scontext=[^\\\\s]+)", 0, 1, "");
}
if ($msg contains "avc:") then {
    set $!rhel8_msg_type = "SELINUX";
}

# Network management (RHEL 8 versions)
if ($programname == "NetworkManager") then {
    set $!rhel8_msg_type = "NETWORK";
}
if ($programname == "dhclient") then {
    set $!rhel8_msg_type = "NETWORK";
}

# Time synchronization (RHEL 8 default: chronyd)
if ($programname == "chronyd") then {
    set $!rhel8_msg_type = "TIME_SYNC";
}

# Firewall (RHEL 8 firewalld)
if ($programname == "firewalld") then {
    set $!rhel8_msg_type = "FIREWALL";
}

# Package management (RHEL 8: yum/dnf)
if ($programname == "yum") then {
    set $!rhel8_msg_type = "PACKAGE_MANAGEMENT";
}
if ($programname == "dnf") then {
    set $!rhel8_msg_type = "PACKAGE_MANAGEMENT";
}
if ($programname == "rpm") then {
    set $!rhel8_msg_type = "PACKAGE_MANAGEMENT";
}

# Scheduled tasks
if ($syslogfacility-text == "cron") then {
    set $!rhel8_msg_type = "SCHEDULED_TASK";
}

# Mail system
if ($syslogfacility-text == "mail") then {
    set $!rhel8_msg_type = "MAIL_SYSTEM";
}

# Default classification
if ($!rhel8_msg_type == "") then {
    set $!rhel8_msg_type = "GENERAL_SYSTEM";
}

#####################################################################
# PRIORITY-BASED FORWARDING (RHEL 8 Legacy Syntax)
#####################################################################

# CRITICAL PRIORITY: Security, audit, authentication messages
# Configure high-priority queue
$ActionQueueFileName rhel8_critical_vector
$ActionQueueMaxDiskSpace 3g
$ActionQueueSaveOnShutdown on
$ActionQueueType LinkedList
$ActionResumeRetryCount -1
$ActionQueueSize 25000
$ActionQueueDiscardMark 23000
$ActionQueueHighWaterMark 20000
$ActionQueueLowWaterMark 5000

# Forward critical messages
# Note: Using direct IP instead of template variable for RHEL 8 compatibility
# Replace ************* with your actual Vector collector IP
*.emerg @@*************:6514;RHEL8VectorFormat
*.alert @@*************:6514;RHEL8VectorFormat
*.crit @@*************:6514;RHEL8VectorFormat
authpriv.* @@*************:6514;RHEL8VectorFormat

# Reset queue for medium priority
$ActionQueueFileName rhel8_medium_vector
$ActionQueueMaxDiskSpace 2g
$ActionQueueSize 20000
$ActionQueueDiscardMark 18000
$ActionQueueHighWaterMark 15000
$ActionQueueLowWaterMark 4000

# Forward medium priority messages
kern.* @@*************:6514;RHEL8VectorFormat
daemon.* @@*************:6514;RHEL8VectorFormat
*.err @@*************:6514;RHEL8VectorFormat
*.warning @@*************:6514;RHEL8VectorFormat

# Reset queue for standard priority
$ActionQueueFileName rhel8_standard_vector
$ActionQueueMaxDiskSpace 1g
$ActionQueueSize 15000
$ActionQueueDiscardMark 13000
$ActionQueueHighWaterMark 10000
$ActionQueueLowWaterMark 3000

# Forward all other messages
*.info @@*************:6514;RHEL8VectorFormat

#####################################################################
# LOCAL LOGGING PRESERVATION (RHEL 8 Standard)
#####################################################################

# Reset queue settings for local logging
$ActionQueueFileName
$ActionQueueMaxDiskSpace
$ActionQueueSaveOnShutdown off
$ActionQueueType Direct
$ActionResumeRetryCount
$ActionQueueSize
$ActionQueueDiscardMark
$ActionQueueHighWaterMark
$ActionQueueLowWaterMark

# Standard RHEL 8 local logging
*.info;mail.none;authpriv.none;cron.none    /var/log/messages
authpriv.*                                  /var/log/secure
mail.*                                      /var/log/maillog
cron.*                                      /var/log/cron
*.emerg                                     :omusrmsg:*
uucp,news.crit                             /var/log/spooler
local7.*                                   /var/log/boot.log

# RHEL 8 specific logging
:programname, isequal, "systemd"           /var/log/systemd.log
:msg, contains, "type=AVC"                 /var/log/selinux-avc.log

#####################################################################
# ERROR HANDLING AND MONITORING (RHEL 8)
#####################################################################

# Log rsyslog errors
$ErrorMessagesToStderr off
$LogRSyslogStatusMessages on

# Monitor forwarding errors (RHEL 8 compatible)
$ActionExecOnlyOnceEveryInterval 300
:msg, contains, "suspended"               /var/log/rsyslog-vector-errors.log
:msg, contains, "connection refused"      /var/log/rsyslog-vector-errors.log

#####################################################################
# RHEL 8 DEPLOYMENT VALIDATION
#####################################################################

# RHEL 8 Specific Deployment Steps:
#
# 1. PACKAGE INSTALLATION (RHEL 8):
#    sudo yum install -y rsyslog
#    sudo yum install -y rsyslog-mmjsonparse  # Optional, if available
#    sudo yum install -y nc telnet            # For testing
#
# 2. CONFIGURATION DEPLOYMENT:
#    sudo cp rhel8-vector-forwarder.conf /etc/rsyslog.d/50-rhel8-vector-forwarder.conf
#    sudo chown root:root /etc/rsyslog.d/50-rhel8-vector-forwarder.conf
#    sudo chmod 644 /etc/rsyslog.d/50-rhel8-vector-forwarder.conf
#
# 3. UPDATE VECTOR COLLECTOR IP:
#    sudo sed -i 's/192\.168\.1\.100/YOUR_VECTOR_IP/g' /etc/rsyslog.d/50-rhel8-vector-forwarder.conf
#
# 4. VALIDATION COMMANDS (RHEL 8):
#    rsyslogd -version                    # Should show 8.1911.0 or similar
#    sudo rsyslogd -N1 -f /etc/rsyslog.conf  # Test configuration syntax
#    nc -zv YOUR_VECTOR_IP 6514          # Test connectivity
#
# 5. FIREWALL CONFIGURATION (RHEL 8):
#    sudo firewall-cmd --add-port=6514/tcp --permanent
#    sudo firewall-cmd --reload
#
# 6. SERVICE RESTART:
#    sudo systemctl restart rsyslog
#    sudo systemctl status rsyslog
#
# 7. TESTING:
#    logger "RHEL8 Vector forwarder test from $(hostname) at $(date)"
#    logger -p authpriv.warning "RHEL8 authentication test"
#    logger -p kern.error "RHEL8 kernel test message"
#
# 8. MONITORING:
#    sudo ls -la /var/spool/rsyslog/rhel8_*_vector*
#    sudo tail -f /var/log/rsyslog-vector-errors.log
#    sudo tail -f /var/log/messages | grep rsyslog
#
# 9. RHEL 8 TROUBLESHOOTING:
#    # Check for module loading issues
#    sudo journalctl -u rsyslog | grep -i "module\|error"
#
#    # Verify legacy syntax compatibility
#    sudo rsyslogd -N1 -f /etc/rsyslog.d/50-rhel8-vector-forwarder.conf
#
#    # Check systemd journal integration
#    sudo systemctl status systemd-journald
#
#    # Monitor queue file growth
#    watch -n 5 'ls -lah /var/spool/rsyslog/'

#####################################################################
# END OF RHEL 8 CONFIGURATION
#####################################################################
