#!/bin/bash
# Script to identify exact lines causing "invalid number" errors in rsyslog

echo "=========================================="
echo "Rsyslog Invalid Number Error Line Identifier"
echo "=========================================="

# Function to test configuration with line numbers
test_config_section() {
    local start_line=$1
    local end_line=$2
    local description=$3
    
    echo ""
    echo "Testing $description (lines $start_line-$end_line):"
    echo "----------------------------------------"
    
    # Extract the section and test it
    sed -n "${start_line},${end_line}p" /etc/rsyslog.conf | nl -v$start_line
    
    echo ""
    echo "Errors in this section:"
    # Create a minimal test config with just this section
    cat > /tmp/test_rsyslog.conf << EOF
\$ModLoad imuxsock
\$WorkDirectory /var/lib/rsyslog
EOF
    
    sed -n "${start_line},${end_line}p" /etc/rsyslog.conf >> /tmp/test_rsyslog.conf
    echo "*.* /var/log/test.log" >> /tmp/test_rsyslog.conf
    
    # Test this section
    error_output=$(sudo rsyslogd -N1 -f /tmp/test_rsyslog.conf 2>&1)
    error_count=$(echo "$error_output" | grep -c "invalid number")
    
    if [ $error_count -gt 0 ]; then
        echo "❌ Found $error_count invalid number errors in this section"
        echo "$error_output" | grep "invalid number"
    else
        echo "✅ No invalid number errors in this section"
    fi
}

echo "Current rsyslog configuration analysis:"
echo "File: /etc/rsyslog.conf"
echo ""

# Test the full configuration first
echo "Full configuration test:"
full_output=$(sudo rsyslogd -N1 -f /etc/rsyslog.conf 2>&1)
full_errors=$(echo "$full_output" | grep -c "invalid number")
echo "Total 'invalid number' errors: $full_errors"

if [ $full_errors -eq 0 ]; then
    echo "✅ No errors found - configuration is clean!"
    exit 0
fi

echo ""
echo "Breaking down by sections to identify problematic lines..."

# Test different sections of the config
test_config_section 1 20 "Module loading section"
test_config_section 21 40 "Global directives section"  
test_config_section 90 110 "Queue configuration section"
test_config_section 111 130 "Queue reset and filtering section"

echo ""
echo "=========================================="
echo "DETAILED LINE ANALYSIS"
echo "=========================================="

echo ""
echo "Checking specific queue parameter lines:"

# Check each ActionQueue line individually
grep -n "ActionQueue" /etc/rsyslog.conf | while read line; do
    line_num=$(echo "$line" | cut -d: -f1)
    line_content=$(echo "$line" | cut -d: -f2-)
    
    echo ""
    echo "Line $line_num: $line_content"
    
    # Test just this line
    cat > /tmp/test_single_line.conf << EOF
\$ModLoad imuxsock
\$WorkDirectory /var/lib/rsyslog
$line_content
*.* /var/log/test.log
EOF
    
    single_error=$(sudo rsyslogd -N1 -f /tmp/test_single_line.conf 2>&1)
    single_count=$(echo "$single_error" | grep -c "invalid number")
    
    if [ $single_count -gt 0 ]; then
        echo "❌ This line causes invalid number error"
        echo "   Error: $(echo "$single_error" | grep "invalid number" | head -1)"
    else
        echo "✅ This line is OK"
    fi
done

echo ""
echo "=========================================="
echo "RECOMMENDATIONS"
echo "=========================================="

echo ""
echo "Based on the analysis above, the problematic lines are likely:"
echo "1. Advanced queue sizing parameters not supported in RHEL 8"
echo "2. Numeric parameters with values that can't be parsed"
echo "3. Empty or malformed parameter values"
echo ""
echo "Common fixes:"
echo "- Remove ActionQueueSize, ActionQueueDiscardMark, etc."
echo "- Use only basic queue parameters supported in RHEL 8"
echo "- Apply the corrected rsyslog.conf.fixed configuration"

# Cleanup
rm -f /tmp/test_rsyslog.conf /tmp/test_single_line.conf

echo ""
echo "Analysis completed: $(date)"
echo "=========================================="
