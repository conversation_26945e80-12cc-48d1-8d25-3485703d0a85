# RHEL 8 Rsyslog Configuration Troubleshooting Guide

## Issues Identified and Fixed

### 1. Missing `omfwd` Module Error

**Error:**
```
rsyslogd: could not load module 'omfwd', errors: trying to load module /usr/lib64/rsyslog/omfwd.so: /usr/lib64/rsyslog/omfwd.so: cannot open shared object file: No such file or directory
```

**Root Cause:**
- The `omfwd` module is not available in the base RHEL 8 rsyslog installation
- The shared object file `/usr/lib64/rsyslog/omfwd.so` does not exist

**Solutions:**

**Option 1: Install Additional Packages**
```bash
# Install additional rsyslog modules
sudo yum install -y rsyslog-relp rsyslog-gnutls rsyslog-mmjsonparse

# Check available modules
ls -la /usr/lib64/rsyslog/
```

**Option 2: Use Built-in TCP Forwarding (Recommended)**
- Remove explicit module loading: `$ModLoad omfwd`
- Use built-in TCP forwarding syntax: `*.* @@hostname:port`
- This works without explicit module loading in RHEL 8

### 2. Duplicate Module Loading Errors

**Error:**
```
rsyslogd: module 'imuxsock' already in this config, cannot be added
rsyslogd: module 'imjournal' already in this config, cannot be added
```

**Root Cause:**
- Modules are already loaded in the main `/etc/rsyslog.conf` file
- Attempting to load them again in the custom configuration causes conflicts

**Solution:**
```bash
# Check what modules are already loaded
grep -i "module\|modload" /etc/rsyslog.conf

# Remove duplicate module loading from custom config
# Comment out or remove these lines:
# $ModLoad imuxsock
# $ModLoad imjournal
# $ModLoad imklog
```

### 3. Syntax Errors Around Line 120

**Error:**
```
rsyslogd: error during parsing file /etc/rsyslog.d/50-rhel8-vector-forwarder.conf, on or before line 120: invalid character ':' - is there an invalid escape sequence somewhere?
rsyslogd: error during parsing file /etc/rsyslog.d/50-rhel8-vector-forwarder.conf, on or before line 120: syntax error on token '{'
```

**Root Cause:**
- Incorrect conditional syntax mixing legacy and modern rsyslog formats
- Malformed rule structure: `:syslogfacility-text, isequal, "daemon" /tmp/rsyslog-work`
- Missing action in the rule

**Original Problematic Code:**
```
:syslogfacility-text, isequal, "daemon" /tmp/rsyslog-work
& :programname, isequal, "systemd" {
    set $!rhel8_msg_type = "SYSTEMD_DAEMON";
}
```

**Fixed Code:**
```
if ($syslogfacility-text == "daemon" and $programname == "systemd") then {
    set $!rhel8_msg_type = "SYSTEMD_DAEMON";
}
```

### 4. Template Variable Issues

**Error:**
- Backtick execution with `@@`echo $VectorCollectorIP`:6514` causing parsing issues

**Root Cause:**
- Complex template variable expansion not working reliably in RHEL 8
- Backtick execution in forwarding rules causing syntax errors

**Solution:**
- Use direct IP addresses instead of template variables
- Replace: `@@`echo $VectorCollectorIP`:6514`
- With: `@@*************:6514`

## Complete Fix Implementation

### Step 1: Backup Current Configuration
```bash
sudo cp /etc/rsyslog.d/50-rhel8-vector-forwarder.conf /etc/rsyslog.d/50-rhel8-vector-forwarder.conf.backup
```

### Step 2: Deploy Fixed Configuration
```bash
# Copy the fixed configuration
sudo cp rhel8-vector-forwarder-fixed.conf /etc/rsyslog.d/50-rhel8-vector-forwarder.conf

# Set proper permissions
sudo chown root:root /etc/rsyslog.d/50-rhel8-vector-forwarder.conf
sudo chmod 644 /etc/rsyslog.d/50-rhel8-vector-forwarder.conf
```

### Step 3: Update Vector Collector IP
```bash
# Replace placeholder IP with your actual Vector collector IP
sudo sed -i 's/192\.168\.1\.100/YOUR_ACTUAL_VECTOR_IP/g' /etc/rsyslog.d/50-rhel8-vector-forwarder.conf
```

### Step 4: Validate Configuration
```bash
# Test configuration syntax
sudo rsyslogd -N1 -f /etc/rsyslog.conf

# Should show no errors if fixed correctly
```

### Step 5: Test Connectivity
```bash
# Test network connectivity to Vector collector
nc -zv YOUR_VECTOR_IP 6514

# Should show: Connection to YOUR_VECTOR_IP 6514 port [tcp/*] succeeded!
```

### Step 6: Restart and Verify
```bash
# Restart rsyslog service
sudo systemctl restart rsyslog

# Check service status
sudo systemctl status rsyslog

# Verify no errors in logs
sudo journalctl -u rsyslog --since "5 minutes ago"
```

### Step 7: Test Log Forwarding
```bash
# Send test messages
logger "RHEL8 Vector forwarder test from $(hostname)"
logger -p authpriv.warning "RHEL8 authentication test"
logger -p kern.error "RHEL8 kernel test message"

# Check queue files are created
sudo ls -la /var/spool/rsyslog/rhel8_*_vector*

# Monitor for errors
sudo tail -f /var/log/rsyslog-vector-errors.log
```

## Key Changes Made

1. **Removed explicit module loading** to avoid conflicts
2. **Fixed conditional syntax** from legacy format to proper if-then statements
3. **Simplified forwarding rules** using direct IP addresses
4. **Reduced queue sizes** for better resource management
5. **Added comprehensive error handling** and monitoring
6. **Included detailed deployment instructions** and troubleshooting steps

## Verification Commands

```bash
# Check configuration syntax
sudo rsyslogd -N1 -f /etc/rsyslog.conf

# Monitor rsyslog service
sudo journalctl -u rsyslog -f

# Check queue file growth
watch -n 5 'ls -lah /var/spool/rsyslog/'

# Test network connectivity
nc -zv YOUR_VECTOR_IP 6514

# Send test messages
logger "Test message from $(hostname)"
```

## Additional Recommendations

1. **Install monitoring tools:**
   ```bash
   sudo yum install -y nc telnet netstat-nat
   ```

2. **Set up log rotation for error logs:**
   ```bash
   sudo vi /etc/logrotate.d/rsyslog-vector
   ```

3. **Monitor disk space usage:**
   ```bash
   df -h /var/spool/rsyslog/
   ```

4. **Consider firewall rules:**
   ```bash
   sudo firewall-cmd --add-port=6514/tcp --permanent
   sudo firewall-cmd --reload
   ```
